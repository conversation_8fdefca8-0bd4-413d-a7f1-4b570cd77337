/**
 * Configuração das URLs das APIs
 * Resolve problemas de CORS usando proxy em desenvolvimento
 */

const isDevelopment = process.env.NODE_ENV === 'development';
const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

// URLs base das APIs
const API_URLS = {
  // API Principal DSM
  DSM_API: isDevelopment && isLocalhost 
    ? '/api/' 
    : process.env.REACT_APP_API_PERMISSION || 'https://api.dsm.darede.com.br/dev/',
  
  // API Cognito
  COGNITO_API: isDevelopment && isLocalhost
    ? '/cognito-api/local'
    : process.env.REACT_APP_COGNITO_PARSE || 'https://lukr7ocjz4.execute-api.us-east-1.amazonaws.com/dev/local/',
  
  // Outras APIs (mantém URLs originais por enquanto)
  OTRS_API: process.env.REACT_APP_API_OTRS_URL || 'https://h1fpnexbuf.execute-api.us-east-1.amazonaws.com/hml',
  BILLING_API: process.env.REACT_APP_API_BILLING_URL || 'https://mbgpd6o2l0.execute-api.us-east-1.amazonaws.com/hml',
  REPORTS_API: process.env.REACT_APP_API_REPORTS_URL || 'https://6rluxgms63.execute-api.us-east-1.amazonaws.com/prod/reports/',
  CUSTOMER_PORTAL_API: process.env.REACT_APP_API_CUSTOMER_PORTAL_API_NEW || 'https://j3bkejuo38.execute-api.us-east-1.amazonaws.com/hml',
};

// Função para obter URL da API DSM
export const getDsmApiUrl = () => API_URLS.DSM_API;

// Função para obter URL da API Cognito
export const getCognitoApiUrl = () => API_URLS.COGNITO_API;

// Função para obter URL de qualquer API
export const getApiUrl = (apiName) => API_URLS[apiName] || '';

// Log para debug
if (isDevelopment) {
  console.log('🔧 API Configuration:', {
    environment: process.env.NODE_ENV,
    isLocalhost,
    dsmApi: API_URLS.DSM_API,
    cognitoApi: API_URLS.COGNITO_API,
  });
}

export default API_URLS;
