import React from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Alert } from 'antd';
import { useNavigate } from 'react-router-dom';
import { authService } from '../../services/httpOnlyAuthService';

const { Title, Text } = Typography;

const DevLogin = () => {
  const navigate = useNavigate();

  const handleDevLogin = () => {
    console.log('🚀 Login direto para desenvolvimento');
    
    // Simular dados de usuário para desenvolvimento
    const mockUser = {
      email: '<EMAIL>',
      name: 'Desenvolvedor',
      role: 'admin'
    };
    
    // Definir permissão de admin
    authService.setUserPermission('admin');
    
    // Salvar dados do usuário
    localStorage.setItem('userEmail', mockUser.email);
    localStorage.setItem('userName', mockUser.name);
    
    console.log('✅ Login de desenvolvimento realizado com sucesso');
    navigate('/');
  };

  // Só mostrar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    navigate('/login');
    return null;
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      backgroundColor: '#f0f2f5'
    }}>
      <Card style={{ width: 400, textAlign: 'center' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Title level={3}>🔧 Modo Desenvolvimento</Title>
          
          <Alert
            message="Ambiente de Desenvolvimento"
            description="Esta página permite entrar no sistema diretamente para desenvolvimento local."
            type="info"
            showIcon
          />
          
          <Text type="secondary">
            Clique no botão abaixo para fazer login como administrador
          </Text>
          
          <Button 
            type="primary" 
            size="large" 
            onClick={handleDevLogin}
            style={{ width: '100%' }}
          >
            🚀 Entrar como Admin
          </Button>
          
          <Button 
            type="link" 
            onClick={() => navigate('/login')}
          >
            Voltar para Login Normal
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default DevLogin;
