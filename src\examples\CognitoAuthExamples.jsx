/**
 * Exemplos de uso do AuthProvider com Cognito e Cookies HttpOnly
 * Demonstra todas as funcionalidades implementadas
 */

import React, { useState } from 'react';
import { 
  Card, 
  Space, 
  Button, 
  Typography, 
  Alert, 
  Descriptions, 
  Tag, 
  Divider,
  Row,
  Col,
  Modal,
  Input
} from 'antd';
import {
  LoginOutlined,
  LogoutOutlined,
  ReloadOutlined,
  UserOutlined,
  SafetyOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { 
  useAuth, 
  useAuthStatus, 
  usePermissions, 
  useCurrentUser 
} from '../hooks/useCognitoAuth';

const { Title, Text, Paragraph } = Typography;

/**
 * Exemplo 1: Status de Autenticação
 */
const AuthStatusExample = () => {
  const authStatus = useAuthStatus();
  const auth = useAuth();

  const getStatusColor = (status) => {
    switch (status) {
      case 'authenticated': return 'success';
      case 'loading': return 'processing';
      case 'unauthenticated': return 'default';
      default: return 'error';
    }
  };

  return (
    <Card title="Status de Autenticação" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Descriptions column={1} size="small">
          <Descriptions.Item label="Status">
            <Tag color={getStatusColor(authStatus.status)}>
              {authStatus.status.toUpperCase()}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Método de Auth">
            {auth.authMethod || 'Não definido'}
          </Descriptions.Item>
          <Descriptions.Item label="HttpOnly Suportado">
            {auth.httpOnlySupported ? '✅ Sim' : '❌ Não'}
          </Descriptions.Item>
          <Descriptions.Item label="Amplify Inicializado">
            {auth.amplifyInitialized ? '✅ Sim' : '❌ Não'}
          </Descriptions.Item>
        </Descriptions>

        {auth.error && (
          <Alert
            message="Erro de Autenticação"
            description={auth.error}
            type="error"
            showIcon
          />
        )}
      </Space>
    </Card>
  );
};

/**
 * Exemplo 2: Informações do Usuário
 */
const UserInfoExample = () => {
  const user = useCurrentUser();
  const auth = useAuth();

  if (!user.isLoaded) {
    return (
      <Card title="Informações do Usuário" size="small">
        <Text type="secondary">Usuário não autenticado</Text>
      </Card>
    );
  }

  return (
    <Card title="Informações do Usuário" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Descriptions column={1} size="small">
          <Descriptions.Item label="Nome">
            {user.displayName}
          </Descriptions.Item>
          <Descriptions.Item label="Email">
            {user.email}
          </Descriptions.Item>
          <Descriptions.Item label="Iniciais">
            {user.initials}
          </Descriptions.Item>
          <Descriptions.Item label="Grupos">
            <Space wrap>
              {auth.user?.groups?.map((group, index) => (
                <Tag key={index} color="blue">{group}</Tag>
              )) || <Text type="secondary">Nenhum</Text>}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="Permissões">
            <Space wrap>
              {auth.user?.permissions?.map((permission, index) => (
                <Tag key={index} color="green">{permission}</Tag>
              )) || <Text type="secondary">Nenhuma</Text>}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Space>
    </Card>
  );
};

/**
 * Exemplo 3: Controles de Autenticação
 */
const AuthControlsExample = () => {
  const auth = useAuth();
  const [testCode, setTestCode] = useState('');
  const [showCodeModal, setShowCodeModal] = useState(false);

  const handleTestLogin = async () => {
    if (!testCode) {
      Modal.error({
        title: 'Código Necessário',
        content: 'Por favor, insira um código de teste para simular o login.'
      });
      return;
    }

    try {
      await auth.login(testCode);
      setShowCodeModal(false);
      setTestCode('');
      Modal.success({
        title: 'Login Realizado',
        content: 'Login realizado com sucesso!'
      });
    } catch (error) {
      Modal.error({
        title: 'Erro no Login',
        content: error.message
      });
    }
  };

  const handleLogout = async () => {
    Modal.confirm({
      title: 'Confirmar Logout',
      content: 'Tem certeza que deseja sair?',
      onOk: async () => {
        try {
          await auth.logoutWithRedirect();
        } catch (error) {
          Modal.error({
            title: 'Erro no Logout',
            content: error.message
          });
        }
      }
    });
  };

  const handleRefreshToken = async () => {
    try {
      const success = await auth.refreshToken();
      Modal.success({
        title: 'Token Atualizado',
        content: success ? 'Token renovado com sucesso!' : 'Falha na renovação do token'
      });
    } catch (error) {
      Modal.error({
        title: 'Erro no Refresh',
        content: error.message
      });
    }
  };

  return (
    <Card title="Controles de Autenticação" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Space wrap>
          {!auth.isAuthenticated ? (
            <>
              <Button 
                type="primary" 
                icon={<LoginOutlined />}
                onClick={() => setShowCodeModal(true)}
                loading={auth.isLoading}
              >
                Testar Login
              </Button>
              <Button 
                icon={<LoginOutlined />}
                onClick={auth.redirectToLogin}
              >
                Login OAuth
              </Button>
            </>
          ) : (
            <>
              <Button 
                danger
                icon={<LogoutOutlined />}
                onClick={handleLogout}
                loading={auth.isLoading}
              >
                Logout
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={handleRefreshToken}
              >
                Refresh Token
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={auth.checkAuth}
              >
                Verificar Auth
              </Button>
            </>
          )}
        </Space>

        {auth.sessionWarning && (
          <Alert
            message="Sessão Expirando"
            description="Sua sessão expirará em breve devido à inatividade."
            type="warning"
            showIcon
            action={
              <Button size="small" onClick={auth.extendSession}>
                Estender Sessão
              </Button>
            }
          />
        )}
      </Space>

      <Modal
        title="Testar Login"
        open={showCodeModal}
        onOk={handleTestLogin}
        onCancel={() => setShowCodeModal(false)}
        okText="Login"
        cancelText="Cancelar"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>Insira um código de teste para simular o processo de login:</Text>
          <Input
            placeholder="Código de autenticação"
            value={testCode}
            onChange={(e) => setTestCode(e.target.value)}
            onPressEnter={handleTestLogin}
          />
          <Alert
            message="Modo de Teste"
            description="Este é um teste do fluxo de autenticação. Em produção, o código seria obtido automaticamente do OAuth."
            type="info"
            showIcon
          />
        </Space>
      </Modal>
    </Card>
  );
};

/**
 * Exemplo 4: Verificação de Permissões
 */
const PermissionsExample = () => {
  const permissions = usePermissions(['admin', 'manager', 'user', 'read', 'write']);
  const auth = useAuth();

  return (
    <Card title="Verificação de Permissões" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Descriptions column={1} size="small">
          <Descriptions.Item label="Tem permissão 'admin'">
            {permissions.hasPermission('admin') ? '✅ Sim' : '❌ Não'}
          </Descriptions.Item>
          <Descriptions.Item label="Tem alguma permissão">
            {permissions.hasAny ? '✅ Sim' : '❌ Não'}
          </Descriptions.Item>
          <Descriptions.Item label="Tem todas as permissões">
            {permissions.hasAll ? '✅ Sim' : '❌ Não'}
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        <Text strong>Permissões do Usuário:</Text>
        <Space wrap>
          {permissions.userPermissions.map((permission, index) => (
            <Tag key={index} color="green">{permission}</Tag>
          ))}
          {permissions.userPermissions.length === 0 && (
            <Text type="secondary">Nenhuma permissão</Text>
          )}
        </Space>

        {permissions.missing.length > 0 && (
          <>
            <Text strong>Permissões Faltando:</Text>
            <Space wrap>
              {permissions.missing.map((permission, index) => (
                <Tag key={index} color="red">{permission}</Tag>
              ))}
            </Space>
          </>
        )}
      </Space>
    </Card>
  );
};

/**
 * Exemplo 5: Guards de Rota
 */
const RouteGuardsExample = () => {
  const auth = useAuth();

  const testRequireAuth = () => {
    const result = auth.requireAuth();
    Modal.info({
      title: 'Teste requireAuth',
      content: result ? 'Usuário autenticado!' : 'Redirecionamento necessário'
    });
  };

  const testRequirePermission = () => {
    const result = auth.requirePermission('admin');
    Modal.info({
      title: 'Teste requirePermission',
      content: result ? 'Permissão concedida!' : 'Acesso negado'
    });
  };

  const testRequireAnyPermission = () => {
    const result = auth.requireAnyPermission(['admin', 'manager']);
    Modal.info({
      title: 'Teste requireAnyPermission',
      content: result ? 'Pelo menos uma permissão encontrada!' : 'Nenhuma permissão válida'
    });
  };

  return (
    <Card title="Guards de Rota" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Text>Teste os guards de rota (simulação):</Text>
        <Space wrap>
          <Button onClick={testRequireAuth}>
            Testar requireAuth
          </Button>
          <Button onClick={testRequirePermission}>
            Testar requirePermission
          </Button>
          <Button onClick={testRequireAnyPermission}>
            Testar requireAnyPermission
          </Button>
        </Space>
      </Space>
    </Card>
  );
};

/**
 * Componente principal de exemplos
 */
export const CognitoAuthExamples = () => {
  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>
        <SafetyOutlined /> Exemplos AuthProvider com Cognito
      </Title>
      <Paragraph>
        Esta página demonstra todas as funcionalidades do AuthProvider integrado com Amazon Cognito e cookies HttpOnly.
      </Paragraph>

      <Alert
        message="Sistema de Autenticação Avançado"
        description="Integração completa com AWS Cognito, detecção automática de suporte HttpOnly, refresh automático de tokens e gerenciamento inteligente de sessão."
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: '24px' }}
      />

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <AuthStatusExample />
        </Col>
        <Col span={12}>
          <UserInfoExample />
        </Col>
        <Col span={24}>
          <AuthControlsExample />
        </Col>
        <Col span={12}>
          <PermissionsExample />
        </Col>
        <Col span={12}>
          <RouteGuardsExample />
        </Col>
      </Row>

      <Divider />

      <Alert
        message="Funcionalidades Implementadas"
        description={
          <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
            <li>✅ Integração completa com Amazon Cognito (userPoolId: us-east-1_VCf8aHRIZ)</li>
            <li>✅ Detecção automática de suporte a cookies HttpOnly</li>
            <li>✅ Login/logout com redirecionamento automático</li>
            <li>✅ Verificação de autenticação e permissões</li>
            <li>✅ Gerenciamento de estado de loading e erros</li>
            <li>✅ Interceptors do Axios com refresh automático</li>
            <li>✅ Monitoramento de atividade e sessão</li>
            <li>✅ Guards de rota e proteção de componentes</li>
            <li>✅ Hooks especializados para diferentes casos de uso</li>
            <li>✅ Fallback inteligente entre métodos de autenticação</li>
          </ul>
        }
        type="success"
        showIcon
      />
    </div>
  );
};

export default CognitoAuthExamples;
