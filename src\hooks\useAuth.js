import { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { httpOnlyAuthService } from '../services/httpOnlyAuthService';
import { logger } from '../utils/logger';

/**
 * Hook personalizado para gerenciar autenticação
 * Implementa melhores práticas de segurança e gerenciamento de estado
 */
export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  /**
   * Verifica status de autenticação - OTIMIZADO
   */
  const checkAuthStatus = useCallback(async () => {
    try {
      setIsLoading(true);

      // Usar método mais robusto que verifica backend se disponível
      const authStatus = await httpOnlyAuthService.getAuthStatus();

      if (authStatus.authenticated && authStatus.user) {
        setUser(authStatus.user);
        setIsAuthenticated(true);
        logger.debug('Usuário autenticado', {
          email: authStatus.user.email,
          method: authStatus.method
        });
      } else {
        setUser(null);
        setIsAuthenticated(false);
        logger.debug('Usuário não autenticado', {
          method: authStatus.method,
          error: authStatus.error
        });
      }
    } catch (error) {
      logger.error('Erro ao verificar autenticação', { error: error.message });
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []); // Dependências vazias - função estável

  /**
   * Faz login do usuário
   */
  const login = useCallback(async (code) => {
    try {
      setIsLoading(true);
      const { user: userData } = await httpOnlyAuthService.authenticate(code);

      setUser(userData);
      setIsAuthenticated(true);

      logger.info('Login realizado com sucesso', { email: userData.email });
      return userData;
    } catch (error) {
      logger.error('Erro no login', { error: error.message });
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Faz logout do usuário
   */
  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await httpOnlyAuthService.logout();

      setUser(null);
      setIsAuthenticated(false);

      logger.info('Logout realizado com sucesso');
      navigate('/login');
    } catch (error) {
      logger.error('Erro no logout', { error: error.message });
      // Mesmo com erro, limpa estado local
      setUser(null);
      setIsAuthenticated(false);
      navigate('/login');
    } finally {
      setIsLoading(false);
    }
  }, [navigate]);

  /**
   * Atualiza permissão do usuário
   */
  const updateUserPermission = useCallback((permission) => {
    httpOnlyAuthService.setUserPermission(permission);
    setUser(prev => prev ? { ...prev, permission } : null);
  }, []);

  /**
   * Funções memoizadas para evitar re-renders desnecessários
   */
  const authUtils = useMemo(() => ({
    getToken: () => httpOnlyAuthService.getTokenFromCookie(),
    getAuthHeaders: () => httpOnlyAuthService.getAuthHeaders(),
    hasPermission: (requiredPermission) => httpOnlyAuthService.hasPermission(requiredPermission)
  }), []); // Dependências vazias - funções estáveis

  /**
   * Força revalidação da autenticação - OTIMIZADO
   */
  const revalidate = useCallback(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  // Verifica autenticação na inicialização
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  // Configura verificação periódica de token OTIMIZADA
  useEffect(() => {
    if (!isAuthenticated) return;

    let interval;
    let timeoutId;

    // Verificação inteligente baseada em atividade do usuário
    const checkAuthWithBackoff = async (attempt = 1) => {
      try {
        const authenticated = await httpOnlyAuthService.isAuthenticated();
        if (!authenticated) {
          logger.info('Sessão expirada, redirecionando para login');
          setUser(null);
          setIsAuthenticated(false);
          navigate('/login');
          return;
        }

        // Reset do contador de tentativas em caso de sucesso
        attempt = 1;
      } catch (error) {
        logger.debug('Erro na verificação periódica de autenticação', {
          error: error.message,
          attempt
        });

        // Backoff exponencial em caso de erro
        const backoffDelay = Math.min(1000 * Math.pow(2, attempt), 300000); // Max 5 minutos
        timeoutId = setTimeout(() => checkAuthWithBackoff(attempt + 1), backoffDelay);
        return;
      }
    };

    // Verificação baseada em visibilidade da página
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        checkAuthWithBackoff();
      }
    };

    // Verificação baseada em atividade do usuário
    const handleUserActivity = () => {
      if (isAuthenticated) {
        // Debounce: só verifica após 30 segundos de inatividade
        clearTimeout(timeoutId);
        timeoutId = setTimeout(checkAuthWithBackoff, 30000);
      }
    };

    // Eventos de atividade do usuário
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

    // Configurar listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    activityEvents.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    // Verificação periódica reduzida (a cada 5 minutos em vez de 1 minuto)
    interval = setInterval(checkAuthWithBackoff, 300000); // 5 minutos

    // Verificação inicial após 30 segundos
    timeoutId = setTimeout(checkAuthWithBackoff, 30000);

    return () => {
      clearInterval(interval);
      clearTimeout(timeoutId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
    };
  }, [isAuthenticated, navigate]);

  return {
    // Estado
    isAuthenticated,
    isLoading,
    user,

    // Ações
    login,
    logout,
    updateUserPermission,
    revalidate,

    // Utilitários otimizados
    ...authUtils
  };
};

export default useAuth;
