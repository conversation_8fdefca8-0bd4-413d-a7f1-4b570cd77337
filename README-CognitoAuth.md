# 🔐 AuthProvider Completo com Cognito e Cookies HttpOnly

## 📋 Visão Geral

Sistema completo de autenticação para React 18 integrando:

- ✅ **Amazon Cognito** (userPoolId: us-east-1_VCf8aHRIZ)
- ✅ **Detecção automática** de suporte a cookies HttpOnly
- ✅ **Login/logout** com redirecionamento automático
- ✅ **Verificação de autenticação** e permissões
- ✅ **Estado de loading** e tratamento de erros
- ✅ **Interceptors do Axios** com refresh automático
- ✅ **Monitoramento de sessão** e inatividade
- ✅ **Proteção de rotas** e componentes
- ✅ **Hooks especializados** para diferentes casos de uso

## 🚀 Instalação e Configuração

### 1. Instalar Dependências

```bash
npm install aws-amplify axios
# ou
yarn add aws-amplify axios
```

### 2. Configurar Variáveis de Ambiente

```env
# .env.development
REACT_APP_API_PERMISSION=https://api.dsm.darede.com.br/dev
REACT_APP_COGNITO_PARSE=https://lukr7ocjz4.execute-api.us-east-1.amazonaws.com/dev/local
REACT_APP_STAGE=dev

# .env.production
REACT_APP_API_PERMISSION=https://api.dsm.darede.com.br/prod
REACT_APP_COGNITO_PARSE=https://production-api.amazonaws.com/prod/local
REACT_APP_STAGE=prod
```

### 3. Configurar no App Principal

```jsx
import React from 'react';
import { CognitoAuthProvider } from './contexts/CognitoAuthProvider';
import { AuthInitializer } from './components/Auth/AuthInitializer';

function App() {
  return (
    <CognitoAuthProvider>
      <AuthInitializer>
        <YourAppContent />
      </AuthInitializer>
    </CognitoAuthProvider>
  );
}
```

## 🔧 Uso dos Hooks

### useAuth - Hook Principal

```jsx
import { useAuth } from './hooks/useCognitoAuth';

const MyComponent = () => {
  const {
    // Estados
    isAuthenticated,
    isLoading,
    user,
    error,
    
    // Métodos
    login,
    logout,
    hasPermission,
    
    // Helpers
    userEmail,
    userName,
    userInitials
  } = useAuth();

  if (isLoading) return <div>Carregando...</div>;
  if (!isAuthenticated) return <div>Faça login</div>;

  return (
    <div>
      <h1>Bem-vindo, {userName}!</h1>
      {hasPermission('admin') && <button>Admin</button>}
      <button onClick={logout}>Sair</button>
    </div>
  );
};
```

### Outros Hooks Especializados

```jsx
// Status rápido
import { useAuthStatus } from './hooks/useCognitoAuth';
const { isAuthenticated, isLoading, status } = useAuthStatus();

// Permissões
import { usePermissions } from './hooks/useCognitoAuth';
const { hasAny, hasAll, missing } = usePermissions(['admin', 'user']);

// Usuário atual
import { useCurrentUser } from './hooks/useCognitoAuth';
const { user, email, displayName, initials } = useCurrentUser();
```

## 🛡️ Proteção de Rotas e Componentes

### PrivateRoute

```jsx
import { PrivateRoute } from './components/Auth/PrivateRoute';

// Rota protegida básica
<Route 
  path="/dashboard" 
  element={
    <PrivateRoute>
      <Dashboard />
    </PrivateRoute>
  } 
/>

// Rota com permissão específica
<Route 
  path="/admin" 
  element={
    <PrivateRoute requiredPermission="admin">
      <AdminPanel />
    </PrivateRoute>
  } 
/>
```

### AuthGuard

```jsx
import { AuthGuard } from './components/Auth/AuthGuard';

const MyPage = () => (
  <div>
    <h1>Página Pública</h1>
    
    <AuthGuard>
      <div>Conteúdo privado</div>
    </AuthGuard>
    
    <AuthGuard requiredPermission="admin">
      <button>Painel Admin</button>
    </AuthGuard>
  </div>
);
```

## 🔄 Fluxo de Autenticação

### 1. Login OAuth

```jsx
const LoginPage = () => {
  const { redirectToLogin } = useAuth();
  
  return (
    <button onClick={redirectToLogin}>
      Entrar com Credenciais Corporativas
    </button>
  );
};
```

### 2. Processar Código de Retorno

```jsx
const MFAPage = () => {
  const { login } = useAuth();
  const [searchParams] = useSearchParams();
  
  useEffect(() => {
    const code = searchParams.get('code');
    if (code) {
      login(code).then(() => {
        // Redirecionamento automático
      }).catch(error => {
        console.error('Erro no login:', error);
      });
    }
  }, []);
  
  return <div>Processando autenticação...</div>;
};
```

### 3. Logout

```jsx
const LogoutButton = () => {
  const { logoutWithRedirect } = useAuth();
  
  return (
    <button onClick={logoutWithRedirect}>
      Sair
    </button>
  );
};
```

## 📁 Estrutura de Arquivos Criados

```
src/
├── config/
│   └── amplify.js                    # Configuração do AWS Amplify
├── contexts/
│   └── CognitoAuthProvider.jsx       # Provider principal
├── hooks/
│   └── useCognitoAuth.js            # Hooks de autenticação
├── components/Auth/
│   ├── AuthInitializer.jsx          # Inicializador com loading
│   ├── LoginForm.jsx                # Formulário de login
│   ├── PrivateRoute.jsx             # Proteção de rotas
│   ├── UserProfile.jsx              # Perfil do usuário
│   └── AuthGuard.jsx                # Proteção de componentes
├── examples/
│   └── CognitoAuthExamples.jsx      # Exemplos de uso
├── tests/
│   └── CognitoAuthProvider.test.js  # Testes unitários
├── docs/
│   └── CognitoAuthSetup.md          # Documentação detalhada
├── constants/
│   └── auth.js                      # Constantes atualizadas
└── AppWithCognitoAuth.jsx           # App exemplo
```

## ⚙️ Funcionalidades Avançadas

### Interceptors Automáticos

```javascript
// Configurados automaticamente pelo AuthProvider
// - Adiciona tokens em requisições internas
// - Refresh automático em erro 401
// - Suporte a cookies HttpOnly e Bearer tokens
```

### Monitoramento de Sessão

```jsx
const SessionMonitor = () => {
  const { sessionWarning, extendSession } = useAuth();
  
  if (sessionWarning) {
    return (
      <Alert
        message="Sessão Expirando"
        description="Sua sessão expirará em breve."
        action={<Button onClick={extendSession}>Estender</Button>}
      />
    );
  }
  
  return null;
};
```

### Verificação de Permissões

```jsx
// Verificação simples
const canEdit = hasPermission('edit');

// Verificação múltipla (qualquer uma)
const canManage = hasAnyPermission(['admin', 'manager']);

// Verificação múltipla (todas)
const canDelete = hasAllPermissions(['admin', 'delete']);
```

## 🔍 Debug e Desenvolvimento

### Logs Automáticos

```javascript
// Habilitar logs detalhados
localStorage.setItem('debug', 'auth:*');

// Logs incluem:
// - Inicialização do Amplify
// - Verificação de suporte HttpOnly
// - Processo de login/logout
// - Refresh de tokens
// - Verificação de permissões
```

### Página de Exemplos

```jsx
// Disponível apenas em desenvolvimento
// http://localhost:3000/examples/auth
import { CognitoAuthExamples } from './examples/CognitoAuthExamples';
```

## 🧪 Testes

```bash
# Executar testes
npm test CognitoAuthProvider.test.js

# Testes incluem:
# - Inicialização do provider
# - Fluxo de login/logout
# - Verificação de permissões
# - Interceptors do Axios
# - Tratamento de erros
```

## 🚨 Tratamento de Erros

```jsx
const ErrorHandler = () => {
  const { error } = useAuth();
  
  if (error) {
    return (
      <Alert
        message="Erro de Autenticação"
        description={error}
        type="error"
        showIcon
      />
    );
  }
  
  return null;
};
```

## 🔒 Segurança

- ✅ **Cookies HttpOnly** para máxima segurança
- ✅ **Refresh automático** de tokens
- ✅ **Verificação granular** de permissões
- ✅ **Logout seguro** com limpeza completa
- ✅ **Monitoramento** de inatividade
- ✅ **Proteção CSRF** via SameSite cookies

## 📱 Responsividade

- ✅ **Mobile-first** design
- ✅ **Touch-friendly** interfaces
- ✅ **Acessibilidade** (ARIA, keyboard navigation)
- ✅ **Breakpoints** para todos os dispositivos

## 🎯 Próximos Passos

1. **Implementar** o AuthProvider no seu projeto
2. **Configurar** as variáveis de ambiente
3. **Testar** o fluxo de autenticação
4. **Personalizar** componentes conforme necessário
5. **Adicionar** testes específicos do projeto

## 📞 Suporte

Para dúvidas ou problemas:
1. Consulte a documentação em `src/docs/CognitoAuthSetup.md`
2. Veja os exemplos em `src/examples/CognitoAuthExamples.jsx`
3. Execute os testes para verificar funcionamento
4. Verifique os logs no console do navegador

---

**🎉 Sistema de Autenticação Completo e Pronto para Produção!**
