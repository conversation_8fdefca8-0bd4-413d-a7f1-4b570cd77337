/**
 * Serviço de Autenticação EXCLUSIVAMENTE HttpOnly
 * Implementação 100% segura sem Bearer tokens ou localStorage
 */

import axios from 'axios';
import { logger } from '../utils/logger';
import { config } from '../utils/validateEnvironment';
import { 
  AUTH_TIMEOUTS, 
  COOKIE_CONFIG, 
  STORAGE_KEYS, 
  AUTH_ROUTES,
  AUTH_ERRORS,
  DEFAULT_VALUES
} from '../constants/auth';

class PureHttpOnlyAuthService {
  constructor() {
    this.baseURL = this.getCorrectApiURL();
    
    // FORÇAR HttpOnly sempre - não há fallback
    this.isHttpOnlySupported = true;
    this.interceptorsConfigured = false;
    
    // Configurar axios para EXCLUSIVAMENTE usar cookies HttpOnly
    this.configureHttpOnlyAxios();
    
    // Limpar TODOS os tokens do localStorage/sessionStorage
    this.clearAllLegacyTokens();
    
    logger.info('🔒 PureHttpOnlyAuthService inicializado (MODO SEGURO EXCLUSIVO)', {
      baseURL: this.baseURL,
      environment: process.env.NODE_ENV,
      stage: process.env.REACT_APP_STAGE,
      httpOnlyOnly: true,
      bearerTokens: false,
      localStorage: false
    });
  }

  /**
   * Obter URL correta da API baseada no ambiente
   */
  getCorrectApiURL() {
    const stage = process.env.REACT_APP_STAGE || 'dev';
    const environment = process.env.NODE_ENV;
    
    // NUNCA usar localhost para autenticação - sempre usar a API real
    const apiUrls = {
      development: 'https://api.dsm.darede.com.br/dev', // Mesmo em dev local, usar API real
      dev: 'https://api.dsm.darede.com.br/dev',
      hml: 'https://api.dsm.darede.com.br/hml',
      production: 'https://api.dsm.darede.com.br/prod'
    };

    const apiUrl = apiUrls[stage] || apiUrls.dev;
    
    logger.info('🌐 URL da API determinada', {
      stage,
      environment,
      apiUrl,
      note: 'Sempre usar API real para autenticação'
    });

    return apiUrl;
  }

  /**
   * Configurar axios EXCLUSIVAMENTE para cookies HttpOnly
   */
  configureHttpOnlyAxios() {
    // Configurações globais do axios para cookies HttpOnly
    axios.defaults.withCredentials = true;
    axios.defaults.headers.common['Content-Type'] = 'application/json';
    axios.defaults.headers.common['Accept'] = 'application/json';
    
    // REMOVER qualquer header Authorization existente
    delete axios.defaults.headers.common['Authorization'];
    delete axios.defaults.headers.common['authorization'];
    
    logger.info('🔧 Axios configurado para HttpOnly exclusivo', {
      withCredentials: true,
      authorizationHeaders: 'REMOVIDOS',
      contentType: 'application/json'
    });
  }

  /**
   * Configurar interceptors APENAS para cookies HttpOnly
   */
  configureHttpOnlyInterceptors() {
    if (this.interceptorsConfigured) {
      logger.debug('Interceptors já configurados, pulando...');
      return;
    }

    // Request interceptor - APENAS cookies, NUNCA Bearer tokens
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        // Verificar se é API interna
        const isInternalAPI = config.url?.includes('api.dsm.darede.com.br') ||
                             config.baseURL?.includes('api.dsm.darede.com.br');

        if (isInternalAPI) {
          // FORÇAR cookies HttpOnly
          config.withCredentials = true;
          
          // GARANTIR que não há headers Authorization
          delete config.headers.Authorization;
          delete config.headers.authorization;
          
          logger.debug('🍪 Requisição configurada para HttpOnly', {
            url: config.url,
            withCredentials: true,
            authHeaders: 'REMOVIDOS'
          });
        } else {
          // APIs externas (Cognito, etc.) - sem cookies
          config.withCredentials = false;
          
          logger.debug('🌐 Requisição externa sem cookies', {
            url: config.url,
            withCredentials: false
          });
        }

        return config;
      },
      (error) => {
        logger.error('❌ Erro no interceptor de request', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor - refresh automático via cookies
    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        logger.debug('✅ Resposta recebida com sucesso', {
          status: response.status,
          url: response.config.url
        });
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Verificar se é erro 401 e não é retry
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          logger.warn('🔄 Token expirado, tentando refresh via cookies HttpOnly');

          try {
            // Tentar refresh via cookies HttpOnly
            await this.refreshTokenHttpOnly();
            
            logger.info('✅ Token refreshed via cookies HttpOnly');
            
            // Garantir que o retry também use cookies
            originalRequest.withCredentials = true;
            delete originalRequest.headers.Authorization;
            delete originalRequest.headers.authorization;
            
            // Retry da requisição original
            return axios(originalRequest);
          } catch (refreshError) {
            logger.error('❌ Falha no refresh via cookies HttpOnly', { 
              error: refreshError.message 
            });
            
            // Se refresh falhou, fazer logout e redirecionar
            await this.logoutHttpOnly();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );

    this.interceptorsConfigured = true;
    
    logger.info('🔧 Interceptors HttpOnly configurados com sucesso', {
      requestInterceptor: !!requestInterceptor,
      responseInterceptor: !!responseInterceptor,
      mode: 'HttpOnly exclusivo'
    });

    // Retornar função de cleanup
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
      this.interceptorsConfigured = false;
      logger.info('🧹 Interceptors HttpOnly removidos');
    };
  }

  /**
   * Limpar TODOS os tokens legados
   */
  clearAllLegacyTokens() {
    const tokensToRemove = [
      // localStorage
      'jwt',
      '@dsm/token',
      '@dsm/name',
      '@dsm/email',
      '@dsm/mail',
      '@dsm/username',
      '@dsm/permission',
      '@dsm/time',
      'token',
      'authToken',
      'access_token',
      'id_token',
      'refresh_token',
      'cognito_token',
      'bearer_token',
      
      // Possíveis variações
      'dsm_token',
      'dsm_access_token',
      'dsm_id_token',
      'dsm_refresh_token'
    ];

    let removedCount = 0;

    // Limpar localStorage
    tokensToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        removedCount++;
      }
    });

    // Limpar sessionStorage
    tokensToRemove.forEach(key => {
      if (sessionStorage.getItem(key)) {
        sessionStorage.removeItem(key);
        removedCount++;
      }
    });

    // Limpar cookies manuais (não HttpOnly)
    const cookiesToRemove = [
      'jwt',
      'token',
      'authToken',
      'dsm_token',
      'access_token'
    ];

    cookiesToRemove.forEach(name => {
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=localhost;`;
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.dsm.darede.com.br;`;
    });

    logger.info('🧹 Tokens legados removidos', {
      removedCount,
      tokensRemoved: tokensToRemove.filter(key => 
        !localStorage.getItem(key) && !sessionStorage.getItem(key)
      ).length,
      note: 'Sistema agora usa APENAS cookies HttpOnly'
    });
  }

  /**
   * Verificar se está autenticado via cookies HttpOnly
   */
  async isAuthenticated() {
    try {
      const response = await axios.get(`${this.baseURL}/auth/verify`, {
        withCredentials: true,
        timeout: AUTH_TIMEOUTS.REQUEST_TIMEOUT
      });

      const isAuth = response.status === 200 && response.data?.authenticated;
      
      logger.debug('🔍 Verificação de autenticação via cookies HttpOnly', {
        authenticated: isAuth,
        user: response.data?.user?.email,
        method: 'HttpOnly cookies'
      });

      return isAuth;
    } catch (error) {
      logger.debug('❌ Não autenticado via cookies HttpOnly', {
        error: error.message,
        status: error.response?.status
      });
      return false;
    }
  }

  /**
   * Obter dados do usuário atual via cookies HttpOnly
   */
  async getCurrentUser() {
    try {
      const response = await axios.get(`${this.baseURL}/auth/verify`, {
        withCredentials: true,
        timeout: AUTH_TIMEOUTS.REQUEST_TIMEOUT
      });

      if (response.data?.user) {
        logger.info('👤 Dados do usuário obtidos via cookies HttpOnly', {
          email: response.data.user.email,
          name: response.data.user.name
        });
        return response.data.user;
      }

      throw new Error('Dados do usuário não encontrados');
    } catch (error) {
      logger.error('❌ Erro ao obter dados do usuário', { error: error.message });
      throw error;
    }
  }

  /**
   * Autenticar com código OAuth via cookies HttpOnly
   */
  async authenticate(code) {
    try {
      if (!code) {
        throw new Error('Código de autenticação não fornecido');
      }

      logger.info('🔐 Iniciando autenticação via cookies HttpOnly', { 
        codeLength: code.length 
      });

      // 1. Trocar código por token via Cognito
      const cognitoResponse = await axios.post(
        process.env.REACT_APP_COGNITO_PARSE,
        { code },
        {
          withCredentials: false, // Cognito não usa cookies
          timeout: AUTH_TIMEOUTS.REQUEST_TIMEOUT,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (!cognitoResponse.data?.data?.id_token) {
        throw new Error('Token não encontrado na resposta do Cognito');
      }

      const { id_token } = cognitoResponse.data.data;

      // 2. Enviar token para backend definir cookie HttpOnly
      const authResponse = await axios.post(
        `${this.baseURL}/auth/set-token`,
        { token: id_token },
        {
          withCredentials: true, // ESSENCIAL para receber cookies HttpOnly
          timeout: AUTH_TIMEOUTS.REQUEST_TIMEOUT,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (authResponse.status !== 200) {
        throw new Error('Falha ao definir cookie HttpOnly no backend');
      }

      // 3. Obter dados do usuário
      const user = await this.getCurrentUser();

      logger.info('✅ Autenticação via cookies HttpOnly bem-sucedida', {
        user: user.email,
        method: 'HttpOnly cookies',
        tokenStored: 'Backend HttpOnly cookie'
      });

      return {
        user,
        method: 'httponly',
        authenticated: true
      };

    } catch (error) {
      logger.error('❌ Erro na autenticação via cookies HttpOnly', {
        error: error.message,
        status: error.response?.status
      });
      throw error;
    }
  }

  /**
   * Refresh token via cookies HttpOnly
   */
  async refreshTokenHttpOnly() {
    try {
      const response = await axios.post(`${this.baseURL}/auth/refresh`, {}, {
        withCredentials: true,
        timeout: AUTH_TIMEOUTS.REQUEST_TIMEOUT
      });

      logger.info('🔄 Token refreshed via cookies HttpOnly', {
        status: response.status
      });

      return response.data;
    } catch (error) {
      logger.error('❌ Erro no refresh via cookies HttpOnly', {
        error: error.message,
        status: error.response?.status
      });
      throw error;
    }
  }

  /**
   * Logout via cookies HttpOnly
   */
  async logoutHttpOnly() {
    try {
      await axios.post(`${this.baseURL}/auth/logout`, {}, {
        withCredentials: true,
        timeout: AUTH_TIMEOUTS.REQUEST_TIMEOUT
      });

      // Limpar qualquer token restante
      this.clearAllLegacyTokens();

      logger.info('🚪 Logout via cookies HttpOnly realizado', {
        method: 'HttpOnly cookies',
        tokensCleared: true
      });

      return true;
    } catch (error) {
      logger.error('❌ Erro no logout via cookies HttpOnly', {
        error: error.message
      });
      
      // Mesmo com erro, limpar tokens locais
      this.clearAllLegacyTokens();
      throw error;
    }
  }

  /**
   * Verificar suporte HttpOnly (sempre true nesta implementação)
   */
  async checkHttpOnlySupport() {
    // Sempre retornar true - este serviço é APENAS HttpOnly
    logger.info('✅ Suporte HttpOnly confirmado (modo exclusivo)');
    return true;
  }
}

// Instância singleton
export const pureHttpOnlyAuthService = new PureHttpOnlyAuthService();
export default pureHttpOnlyAuthService;
