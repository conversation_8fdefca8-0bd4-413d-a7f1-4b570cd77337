/**
 * Exemplos de uso da Autenticação EXCLUSIVAMENTE HttpOnly
 * Demonstra todas as funcionalidades de segurança implementadas
 */

import React, { useState } from 'react';
import { 
  Card, 
  Space, 
  Button, 
  Typography, 
  Alert, 
  Descriptions, 
  Tag, 
  Divider,
  Row,
  Col,
  Modal,
  Input,
  Badge
} from 'antd';
import {
  LoginOutlined,
  LogoutOutlined,
  ReloadOutlined,
  UserOutlined,
  SafetyOutlined,
  InfoCircleOutlined,
  LockOutlined,
  ShieldCheckOutlined,
  CookieOutlined
} from '@ant-design/icons';
import { 
  useHttpOnlyAuth, 
  useHttpOnlyAuthStatus, 
  useHttpOnlyPermissions, 
  useHttpOnlyCurrentUser 
} from '../hooks/usePureHttpOnlyAuth';

const { Title, Text, Paragraph } = Typography;

/**
 * Exemplo 1: Status de Autenticação HttpOnly
 */
const HttpOnlyAuthStatusExample = () => {
  const authStatus = useHttpOnlyAuthStatus();
  const auth = useHttpOnlyAuth();

  const getStatusColor = (status) => {
    switch (status) {
      case 'authenticated': return 'success';
      case 'loading': return 'processing';
      case 'unauthenticated': return 'default';
      default: return 'error';
    }
  };

  return (
    <Card 
      title={
        <span>
          <LockOutlined style={{ color: '#00B050', marginRight: '8px' }} />
          Status de Autenticação HttpOnly
        </span>
      } 
      size="small"
      extra={<Badge status="success" text="Modo Seguro" />}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Descriptions column={1} size="small">
          <Descriptions.Item label="Status">
            <Tag color={getStatusColor(authStatus.status)}>
              {authStatus.status.toUpperCase()}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Método de Auth">
            <Tag color="green">
              <CookieOutlined /> HttpOnly Exclusivo
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Bearer Tokens">
            <Tag color="red">❌ Desabilitados</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="localStorage">
            <Tag color="red">❌ Limpo</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Cookies HttpOnly">
            <Tag color="green">✅ Ativos</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Sistema Inicializado">
            {auth.isInitialized ? (
              <Tag color="green">✅ Sim</Tag>
            ) : (
              <Tag color="orange">⏳ Carregando</Tag>
            )}
          </Descriptions.Item>
        </Descriptions>

        {auth.error && (
          <Alert
            message="Erro de Autenticação HttpOnly"
            description={auth.error}
            type="error"
            showIcon
          />
        )}

        <Alert
          message="Configuração de Segurança"
          description="Este sistema usa EXCLUSIVAMENTE cookies HttpOnly. Nenhum token é armazenado no navegador ou enviado via headers Authorization."
          type="success"
          showIcon
        />
      </Space>
    </Card>
  );
};

/**
 * Exemplo 2: Informações do Usuário HttpOnly
 */
const HttpOnlyUserInfoExample = () => {
  const user = useHttpOnlyCurrentUser();
  const auth = useHttpOnlyAuth();

  if (!user.isLoaded) {
    return (
      <Card 
        title={
          <span>
            <UserOutlined style={{ color: '#00B050', marginRight: '8px' }} />
            Informações do Usuário
          </span>
        } 
        size="small"
      >
        <Text type="secondary">Usuário não autenticado via cookies HttpOnly</Text>
      </Card>
    );
  }

  return (
    <Card 
      title={
        <span>
          <UserOutlined style={{ color: '#00B050', marginRight: '8px' }} />
          Informações do Usuário (HttpOnly)
        </span>
      } 
      size="small"
      extra={<Badge status="success" text="Dados Seguros" />}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Descriptions column={1} size="small">
          <Descriptions.Item label="Nome">
            {user.displayName}
          </Descriptions.Item>
          <Descriptions.Item label="Email">
            {user.email}
          </Descriptions.Item>
          <Descriptions.Item label="Iniciais">
            <Tag color="blue">{user.initials}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Grupos">
            <Space wrap>
              {auth.user?.groups?.map((group, index) => (
                <Tag key={index} color="blue">{group}</Tag>
              )) || <Text type="secondary">Nenhum</Text>}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="Permissões">
            <Space wrap>
              {auth.user?.permissions?.map((permission, index) => (
                <Tag key={index} color="green">{permission}</Tag>
              )) || <Text type="secondary">Nenhuma</Text>}
            </Space>
          </Descriptions.Item>
        </Descriptions>

        <Alert
          message="Dados Obtidos via Cookies HttpOnly"
          description="Todas as informações do usuário são obtidas através de cookies seguros, sem exposição de tokens no frontend."
          type="info"
          showIcon
        />
      </Space>
    </Card>
  );
};

/**
 * Exemplo 3: Controles de Autenticação HttpOnly
 */
const HttpOnlyAuthControlsExample = () => {
  const auth = useHttpOnlyAuth();
  const [testCode, setTestCode] = useState('');
  const [showCodeModal, setShowCodeModal] = useState(false);

  const handleTestLogin = async () => {
    if (!testCode) {
      Modal.error({
        title: 'Código Necessário',
        content: 'Por favor, insira um código de teste para simular o login via HttpOnly.'
      });
      return;
    }

    try {
      await auth.login(testCode);
      setShowCodeModal(false);
      setTestCode('');
      Modal.success({
        title: 'Login HttpOnly Realizado',
        content: 'Login realizado com sucesso via cookies HttpOnly!'
      });
    } catch (error) {
      Modal.error({
        title: 'Erro no Login HttpOnly',
        content: error.message
      });
    }
  };

  const handleLogout = async () => {
    Modal.confirm({
      title: 'Confirmar Logout HttpOnly',
      content: 'Tem certeza que deseja sair? Os cookies HttpOnly serão limpos.',
      onOk: async () => {
        try {
          await auth.logoutWithRedirect();
        } catch (error) {
          Modal.error({
            title: 'Erro no Logout HttpOnly',
            content: error.message
          });
        }
      }
    });
  };

  const handleRefreshToken = async () => {
    try {
      const success = await auth.refreshToken();
      Modal.success({
        title: 'Token HttpOnly Atualizado',
        content: success ? 'Token renovado via cookies HttpOnly!' : 'Falha na renovação do token'
      });
    } catch (error) {
      Modal.error({
        title: 'Erro no Refresh HttpOnly',
        content: error.message
      });
    }
  };

  return (
    <Card 
      title={
        <span>
          <SafetyOutlined style={{ color: '#00B050', marginRight: '8px' }} />
          Controles de Autenticação HttpOnly
        </span>
      } 
      size="small"
      extra={<Badge status="success" text="Seguro" />}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Space wrap>
          {!auth.isAuthenticated ? (
            <>
              <Button 
                type="primary" 
                icon={<LoginOutlined />}
                onClick={() => setShowCodeModal(true)}
                loading={auth.isLoading}
                style={{ backgroundColor: '#00B050', borderColor: '#00B050' }}
              >
                Testar Login HttpOnly
              </Button>
              <Button 
                icon={<LoginOutlined />}
                onClick={auth.redirectToLogin}
              >
                Login OAuth
              </Button>
            </>
          ) : (
            <>
              <Button 
                danger
                icon={<LogoutOutlined />}
                onClick={handleLogout}
                loading={auth.isLoading}
              >
                Logout HttpOnly
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={handleRefreshToken}
                style={{ borderColor: '#00B050', color: '#00B050' }}
              >
                Refresh Token
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={auth.checkAuth}
              >
                Verificar Auth
              </Button>
            </>
          )}
        </Space>

        {auth.sessionWarning && (
          <Alert
            message="Sessão HttpOnly Expirando"
            description="Sua sessão via cookies HttpOnly expirará em breve devido à inatividade."
            type="warning"
            showIcon
            action={
              <Button size="small" onClick={auth.extendSession}>
                Estender Sessão
              </Button>
            }
          />
        )}

        <Alert
          message="Segurança HttpOnly"
          description={
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              <li>🍪 Tokens armazenados em cookies HttpOnly no backend</li>
              <li>🚫 Nenhum token exposto ao JavaScript do frontend</li>
              <li>🔄 Refresh automático via cookies seguros</li>
              <li>🧹 localStorage automaticamente limpo</li>
            </ul>
          }
          type="success"
          showIcon
        />
      </Space>

      <Modal
        title="Testar Login HttpOnly"
        open={showCodeModal}
        onOk={handleTestLogin}
        onCancel={() => setShowCodeModal(false)}
        okText="Login"
        cancelText="Cancelar"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>Insira um código de teste para simular o processo de login HttpOnly:</Text>
          <Input
            placeholder="Código de autenticação"
            value={testCode}
            onChange={(e) => setTestCode(e.target.value)}
            onPressEnter={handleTestLogin}
          />
          <Alert
            message="Modo de Teste HttpOnly"
            description="Este é um teste do fluxo de autenticação exclusivamente via cookies HttpOnly. Em produção, o código seria obtido automaticamente do OAuth."
            type="info"
            showIcon
          />
        </Space>
      </Modal>
    </Card>
  );
};

/**
 * Exemplo 4: Verificação de Permissões HttpOnly
 */
const HttpOnlyPermissionsExample = () => {
  const permissions = useHttpOnlyPermissions(['admin', 'manager', 'user', 'read', 'write']);
  const auth = useHttpOnlyAuth();

  return (
    <Card 
      title={
        <span>
          <ShieldCheckOutlined style={{ color: '#00B050', marginRight: '8px' }} />
          Verificação de Permissões (HttpOnly)
        </span>
      } 
      size="small"
      extra={<Badge status="success" text="Via Cookies" />}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Descriptions column={1} size="small">
          <Descriptions.Item label="Tem permissão 'admin'">
            {permissions.hasPermission('admin') ? (
              <Tag color="green">✅ Sim</Tag>
            ) : (
              <Tag color="red">❌ Não</Tag>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="Tem alguma permissão">
            {permissions.hasAny ? (
              <Tag color="green">✅ Sim</Tag>
            ) : (
              <Tag color="red">❌ Não</Tag>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="Tem todas as permissões">
            {permissions.hasAll ? (
              <Tag color="green">✅ Sim</Tag>
            ) : (
              <Tag color="red">❌ Não</Tag>
            )}
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        <Text strong>Permissões do Usuário (via HttpOnly):</Text>
        <Space wrap>
          {permissions.userPermissions.map((permission, index) => (
            <Tag key={index} color="green">{permission}</Tag>
          ))}
          {permissions.userPermissions.length === 0 && (
            <Text type="secondary">Nenhuma permissão</Text>
          )}
        </Space>

        {permissions.missing.length > 0 && (
          <>
            <Text strong>Permissões Faltando:</Text>
            <Space wrap>
              {permissions.missing.map((permission, index) => (
                <Tag key={index} color="red">{permission}</Tag>
              ))}
            </Space>
          </>
        )}

        <Alert
          message="Verificação Segura de Permissões"
          description="Todas as permissões são verificadas via cookies HttpOnly, garantindo que não há manipulação no frontend."
          type="info"
          showIcon
        />
      </Space>
    </Card>
  );
};

/**
 * Componente principal de exemplos HttpOnly
 */
export const HttpOnlyAuthExamples = () => {
  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>
        <LockOutlined style={{ color: '#00B050' }} /> Exemplos de Autenticação HttpOnly Exclusiva
      </Title>
      <Paragraph>
        Esta página demonstra todas as funcionalidades do sistema de autenticação EXCLUSIVAMENTE via cookies HttpOnly, garantindo máxima segurança.
      </Paragraph>

      <Alert
        message="🔒 Sistema de Autenticação 100% Seguro"
        description="Implementação exclusiva com cookies HttpOnly. Zero tokens expostos no frontend, zero vulnerabilidades XSS relacionadas a autenticação."
        type="success"
        showIcon
        icon={<ShieldCheckOutlined />}
        style={{ marginBottom: '24px' }}
      />

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <HttpOnlyAuthStatusExample />
        </Col>
        <Col span={12}>
          <HttpOnlyUserInfoExample />
        </Col>
        <Col span={24}>
          <HttpOnlyAuthControlsExample />
        </Col>
        <Col span={24}>
          <HttpOnlyPermissionsExample />
        </Col>
      </Row>

      <Divider />

      <Alert
        message="🛡️ Funcionalidades de Segurança Implementadas"
        description={
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                <li>✅ <strong>Cookies HttpOnly exclusivos</strong> - Tokens nunca expostos</li>
                <li>✅ <strong>Bearer tokens desabilitados</strong> - Zero headers Authorization</li>
                <li>✅ <strong>localStorage limpo</strong> - Remoção automática de tokens</li>
                <li>✅ <strong>Interceptors seguros</strong> - withCredentials automático</li>
                <li>✅ <strong>Refresh automático</strong> - Via cookies HttpOnly</li>
              </ul>
            </Col>
            <Col span={12}>
              <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                <li>✅ <strong>Proteção XSS</strong> - Tokens inacessíveis ao JavaScript</li>
                <li>✅ <strong>Proteção CSRF</strong> - SameSite cookies</li>
                <li>✅ <strong>Monitoramento de sessão</strong> - Inatividade automática</li>
                <li>✅ <strong>Verificação de permissões</strong> - Via backend seguro</li>
                <li>✅ <strong>Logout seguro</strong> - Limpeza completa de cookies</li>
              </ul>
            </Col>
          </Row>
        }
        type="success"
        showIcon
      />
    </div>
  );
};

export default HttpOnlyAuthExamples;
