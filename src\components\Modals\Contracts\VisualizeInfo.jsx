import React, { useEffect, useMemo, useState } from "react";
import { Modal, Button, Row, Col, List, Tag, Tabs, Divider } from "antd";
import * as modalViewByPermission from "../../../constants/contractsInfoPerPermission";
import { format, isValid } from "date-fns";
import { tabsData } from "./constants/visualizeInfoData";
import * as controller from "../../../controllers/contracts/contract-controller";
import { SearchInput } from "../../SearchInput";
import {
  dynamoGetById,
  getItemsByDynamicIndex,
} from "../../../service/apiDsmDynamo";
import { ExpandableText } from "../../ExpandableText/ExpandableText";
import {
  filterSignatureDateIfContractIsNotSigned,
  formatHourPoolData,
  formatViewDateFields,
} from "./controllers/visualizeInfo";

export const VisualizeInfo = (props) => {
  const [ellipsis, setEllipsis] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [contract, setContract] = useState([]);
  const [searchField, setSearchField] = useState("");
  const { id, customer_id } = props.contract;
  const { userPermissions } = props;
  const getWindowDimensions = () => {
    const { innerWidth: width, innerHeight: height } = window;
    return { width, height };
  };

  const useWindowDimensions = () => {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions()
    );

    useEffect(() => {
      const handleResize = () => setWindowDimensions(getWindowDimensions());

      window.addEventListener("resize", handleResize);

      return () => window.removeEventListener("resize", handleResize);
    }, []);

    return windowDimensions;
  };

  const { width } = useWindowDimensions();

  useEffect(() => {
    if (showModal && id) {
      getContractInfo();
    }
  }, [showModal, id]);

  const formatContract = (contract, customer) => {
    let formattedContract = {};
    const customerInfo = {
      customer_name: customer?.names?.name,
      customer_cnpj: customer?.cnpj,
    };
    for (let key of Object.entries(contract)) {
      if (typeof key[1] === "object") {
        if (key[1]) {
          Object.entries(key[1]).map((item) => {
            if (key[0] !== "customer") {
              formattedContract[item[0]] = item[1];
            }
          });
        }
      }
    }
    formattedContract = {
      ...formattedContract,
      ...contract,
      ...customerInfo,
    };

    formattedContract = Object.entries(formattedContract).filter(
      (item) => item[1] !== null
    );
    return formattedContract;
  };

  async function getCurentCustomer() {
    try {
      const customer = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-customers`,
        customer_id
      );
      return customer;
    } catch (error) {
      console.log(error);
    }
  }

  async function getContractInfo() {
    let updatedHoursData = [];
    const customer = await getCurentCustomer();
    if (showModal && id) {
      const contract = await controller.getContract(id);
      if (contract) {
        if (contract.hasChangedHours) {
          updatedHoursData = await getItemsByDynamicIndex(
            `${process.env.REACT_APP_STAGE}-consumption-hours`,
            "contract_id",
            id
          );
          updatedHoursData = updatedHoursData
            .sort((a, b) =>
              b.updated_consumption_hours.localeCompare(
                a.updated_consumption_hours
              )
            )
            ?.shift();
        }

        let formattedContract = formatContract(contract, customer);
        formattedContract = formattedContract.filter(
          (item) => typeof item[1] !== "object"
        );
        formattedContract.map((item, index) => {
          modalViewByPermission.contractsView.map((permission) => {
            if (
              permission.key === item[0] &&
              userPermissions.find(
                (item) => item.code === permission.permissionCode
              )
            ) {
              formattedContract[index] = {
                fieldName: permission.fieldName,
                content: permission.fieldName.toLowerCase().includes("data")
                  ? formatViewDateFields(item[1], permission.fieldName)
                  : item[1],
                permissionCode: permission.permissionCode,
                key: permission.key,
              };
            }
          });
        });
        if (Array.isArray(formattedContract)) {
          formattedContract = formattedContract.filter((item) => !item[0]);

          if (contract.hasChangedHours && updatedHoursData) {
            formattedContract.map((item) => {
              if (item && item.key === "total_hours") {
                item.content = updatedHoursData.new_hours || "";
              }
              return item;
            });
          }
        } else {
          formattedContract = [];
        }

        setContract(formattedContract || []);
      }
    } else {
      setContract([]);
    }
  }

  const filteredFields = useMemo(() => {
    if (!contract || !Array.isArray(contract)) {
      return [];
    }

    let currentContract = filterSignatureDateIfContractIsNotSigned(contract);

    if (!currentContract || !Array.isArray(currentContract)) {
      return [];
    }

    return currentContract.filter((item) => {
      if (!item || !item.fieldName || typeof item.fieldName !== 'string') {
        return false;
      }

      const searchTerm = searchField || "";
      return item.fieldName.toLowerCase().includes(searchTerm.toLowerCase());
    });
  }, [searchField, contract]);

  return (
    <>
      <Button
        style={{ padding: "0" }}
        type="text"
        onClick={async () => {
          setShowModal(true);
          setEllipsis(true);
        }}
      >
        <Tag color="#0f9347">Visualizar</Tag>
      </Button>
      <Modal
        width={width > 1000 ? "60%" : "100%"}
        title="Informações do Contrato"
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[<Button onClick={() => setShowModal(false)}>Fechar</Button>]}
      >
        <SearchInput
          onChange={(value) => setSearchField(value)}
          placeholder="Pesquisar por campo"
          style={{ marginBottom: "10px" }}
        />
        <Tabs
          type="card"
          defaultActiveKey="general_info"
          items={tabsData.map((tab) => {
            return {
              disabled: !userPermissions.some(
                (permission) => permission.code === tab.permissionCode
              ),
              key: tab.key,
              label: tab.title,
              children: (
                <Row align="middle" justify="center">
                  <Col span={24}>
                    <List
                      grid={{ gutter: 16, column: 3 }}
                      itemLayout="horizontal"
                      loading={contract?.length === 0}
                      pagination={{ pageSize: 10 }}
                      dataSource={filteredFields.filter((item) => {
                        if (!item || !item.key || !tab || !Array.isArray(tab.fieldKeys)) {
                          return false;
                        }
                        return tab.fieldKeys.includes(item.key);
                      })}
                      renderItem={(item, index) => {
                        const content = item?.content || "";
                        const contentLength = typeof content === 'string' ? content.length : 0;
                        const hasLongContent = contentLength > 50;

                        return (
                          <List.Item
                            extra={<Divider />}
                            key={index}
                            onClick={() => {
                              if (hasLongContent) {
                                setEllipsis(!ellipsis);
                              }
                            }}
                          >
                            <List.Item.Meta
                              title={item?.fieldName || ""}
                              description={
                                <ExpandableText
                                  cursor={hasLongContent ? "pointer" : "auto"}
                                  expanded={ellipsis}
                                  itemId={item?.key || index}
                                  text={
                                    item?.key === "pool_type"
                                      ? formatHourPoolData(content)
                                      : item.key === "installmentsValue"
                                      ? Number(item.content || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })
                                      : content
                                  }
                                  rows={1}
                                  symbol="mais"
                                />
                              }
                            />
                          </List.Item>
                        );
                      }}
                    />
                  </Col>
                </Row>
              ),
            };
          })}
        ></Tabs>
      </Modal>
    </>
  );
};
