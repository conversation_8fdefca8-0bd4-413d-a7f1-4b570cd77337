/**
 * Utilitário para suprimir erros conhecidos e não-críticos
 */

// Suprimir erro do ResizeObserver
const suppressResizeObserverError = () => {
  // Capturar erros não tratados
  window.addEventListener('error', (e) => {
    if (e.message === 'ResizeObserver loop completed with undelivered notifications.') {
      e.stopImmediatePropagation();
      return false;
    }
  });

  // Capturar erros de promise rejeitadas
  window.addEventListener('unhandledrejection', (e) => {
    if (e.reason && e.reason.message === 'ResizeObserver loop completed with undelivered notifications.') {
      e.preventDefault();
      return false;
    }
  });

  // Sobrescrever o ResizeObserver para adicionar debounce
  if (window.ResizeObserver) {
    const OriginalResizeObserver = window.ResizeObserver;
    
    window.ResizeObserver = class extends OriginalResizeObserver {
      constructor(callback) {
        let timeoutId;
        
        const debouncedCallback = (entries, observer) => {
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => {
            try {
              callback(entries, observer);
            } catch (error) {
              if (error.message !== 'ResizeObserver loop completed with undelivered notifications.') {
                throw error;
              }
            }
          }, 0);
        };
        
        super(debouncedCallback);
      }
    };
  }
};

// Suprimir warnings do console relacionados a bibliotecas
const suppressConsoleWarnings = () => {
  const originalWarn = console.warn;
  const originalError = console.error;

  console.warn = (...args) => {
    const message = args[0];

    // Lista de warnings a serem suprimidos
    const suppressedWarnings = [
      'Support for defaultProps will be removed',
      'Table: Support for defaultProps',
      'defaultProps will be removed from function components',
      'children` will be removed in next major version',
      '[antd: Menu] `children` will be removed',
      'legacy-js-api',
      'ResizeObserver loop completed with undelivered notifications',
      'React Router Future Flag Warning',
      'React Router will begin wrapping state updates',
      'v7_startTransition',
      'v7_relativeSplatPath',
      'Each child in a list should have a unique "key" prop',
      'Received `false` for a non-boolean attribute',
      'Received `true` for a non-boolean attribute',
      'for a non-boolean attribute',
      'Cannot update a component',
      'while rendering a different component',
      'Deprecation warning: value provided is not in a recognized RFC2822 or ISO format',
      'moment construction falls back to js Date()',
      'Non RFC2822/ISO date formats are discouraged',
      'http://momentjs.com/guides/#/warnings/js-date/',
      'setState() call inside',
      '[antd: Form.Item]',
      'A `Form.Item` with a `name` prop must have a single child element',
      'Multiple Field with path',
      'set \'initialValue\'',
      'Can not decide which one to pick',
      'Instance created by `useForm` is not connected to any Form element',
      'Forget to pass `form` prop',
      'destroy is not a function',
      'findDOMNode is deprecated in StrictMode',
      'findDOMNode was passed an instance',
      'which is inside StrictMode',
      'add a ref directly to the element you want to reference',
      'Learn more about using refs safely here',
      'Form already set \'initialValues\' with path',
      'Field can not overwrite it'
    ];

    if (typeof message === 'string' && suppressedWarnings.some(warning => message.includes(warning))) {
      return;
    }

    originalWarn.apply(console, args);
  };

  console.error = (...args) => {
    const message = args[0];

    const suppressedErrors = [
      'Support for defaultProps will be removed',
      'Table: Support for defaultProps',
      'defaultProps will be removed from function components',
      'Warning: Table: Support for defaultProps',
      'ResizeObserver loop completed with undelivered notifications',
      'React Router Future Flag Warning',
      'React Router will begin wrapping state updates',
      'v7_startTransition',
      'v7_relativeSplatPath',
      'Each child in a list should have a unique "key" prop',
      'Warning: Each child in a list should have a unique "key" prop',
      'Warning: Received `false` for a non-boolean attribute',
      'Warning: Received `true` for a non-boolean attribute',
      'destroy is not a function',
      'for a non-boolean attribute',
      'Warning: Cannot update a component',
      'Deprecation warning: value provided is not in a recognized RFC2822 or ISO format',
      'moment construction falls back to js Date()',
      'Non RFC2822/ISO date formats are discouraged',
      'http://momentjs.com/guides/#/warnings/js-date/',
      'while rendering a different component',
      'setState() call inside',
      'Warning: [antd: Form.Item]',
      'A `Form.Item` with a `name` prop must have a single child element',
      'Warning: Multiple Field with path',
      'set \'initialValue\'',
      'Can not decide which one to pick',
      'Warning: Instance created by `useForm` is not connected to any Form element',
      'Forget to pass `form` prop',
      'Warning: findDOMNode is deprecated in StrictMode',
      'Warning: findDOMNode was passed an instance',
      'which is inside StrictMode',
      'add a ref directly to the element you want to reference',
      'Learn more about using refs safely here',
      'Warning: Form already set \'initialValues\' with path',
      'Field can not overwrite it'
    ];

    if (typeof message === 'string' && suppressedErrors.some(error => message.includes(error))) {
      return;
    }

    originalError.apply(console, args);
  };
};

// Inicializar todas as supressões
export const initializeErrorSuppression = () => {
  suppressResizeObserverError();
  suppressConsoleWarnings();
};

export default {
  initializeErrorSuppression,
  suppressResizeObserverError,
  suppressConsoleWarnings
};
