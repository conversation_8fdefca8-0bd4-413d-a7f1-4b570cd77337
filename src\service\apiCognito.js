import Axios from "axios";
import useSWR from "swr";
import { authService } from "../services/authService";
import { getDsmApiUrl } from "../config/apiConfig";

export function useCognitoGet() {
  const { data, error, mutate } = useSWR(
    getDsmApiUrl() + "cognito/read",
    async () => {
      const { data } = await Axios.get(
        getDsmApiUrl() + "cognito/read",
        {
          headers: authService.getAuthHeaders(),
        }
      );

      return data.data;
    }
  );

  return { data, error, mutate };
}

export const cognitoGet = async () => {
  const { data } = await Axios.get(
    getDsmApiUrl() + "cognito/read",
    {
      headers: authService.getAuthHeaders(),
    }
  );

  return data;
};

export const cognitoPutStatus = async (body) => {
  const { data } = await Axios.put(
    getDsmApiUrl() + "cognito/update/status",
    { ...body },
    {
      headers: authService.getLegacyAuthHeaders(),
    }
  );

  return data;
};

export const cognitoPutRole = async (body) => {
  const { data } = await Axios.put(
    getDsmApiUrl() + "cognito/update/role",
    { ...body },
    {
      headers: authService.getLegacyAuthHeaders(),
    }
  );

  return data;
};
