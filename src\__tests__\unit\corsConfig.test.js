/**
 * Testes unitários para configuração CORS
 * Verifica se as configurações CORS estão corretas
 */

import {
  isInternalAPI,
  isExternalAPI,
  getCorsConfig,
  createAxiosConfig,
  validateCorsConfig,
  CORS_CONFIG
} from '../../config/corsConfig';

describe('Configuração CORS', () => {
  describe('isInternalAPI', () => {
    test('deve identificar APIs internas corretamente', () => {
      expect(isInternalAPI('/api/test')).toBe(true);
      expect(isInternalAPI('api.dsm.darede.com.br/test')).toBe(true);
      expect(isInternalAPI('/dev/test', 'https://api.dsm.darede.com.br')).toBe(true);
    });

    test('deve rejeitar APIs externas', () => {
      expect(isInternalAPI('https://cognito.amazonaws.com')).toBe(false);
      expect(isInternalAPI('https://external-api.com')).toBe(false);
    });

    test('deve lidar com URLs nulas ou indefinidas', () => {
      expect(isInternalAPI(null)).toBe(false);
      expect(isInternalAPI(undefined)).toBe(false);
      expect(isInternalAPI('')).toBe(false);
    });
  });

  describe('isExternalAPI', () => {
    test('deve identificar APIs externas corretamente', () => {
      expect(isExternalAPI('https://cognito.amazonaws.com')).toBe(true);
      expect(isExternalAPI('https://auth0.com/api')).toBe(true);
    });

    test('deve rejeitar APIs internas', () => {
      expect(isExternalAPI('/api/test')).toBe(false);
      expect(isExternalAPI('api.dsm.darede.com.br')).toBe(false);
    });
  });

  describe('getCorsConfig', () => {
    test('deve configurar withCredentials=true para APIs internas', () => {
      const config = getCorsConfig('/api/test');
      
      expect(config.withCredentials).toBe(true);
      expect(config.headers).toEqual(CORS_CONFIG.DEFAULT_HEADERS);
      expect(config.timeout).toBe(CORS_CONFIG.DEFAULT_TIMEOUT);
    });

    test('deve configurar withCredentials=false para APIs externas', () => {
      const config = getCorsConfig('https://cognito.amazonaws.com');
      
      expect(config.withCredentials).toBe(false);
      expect(config.headers).toEqual(CORS_CONFIG.DEFAULT_HEADERS);
    });

    test('deve permitir override de headers', () => {
      const customHeaders = { 'Authorization': 'Bearer token' };
      const config = getCorsConfig('/api/test', null, { headers: customHeaders });
      
      expect(config.headers).toEqual({
        ...CORS_CONFIG.DEFAULT_HEADERS,
        ...customHeaders
      });
    });

    test('deve permitir override de timeout', () => {
      const config = getCorsConfig('/api/test', null, { timeout: 60000 });
      
      expect(config.timeout).toBe(60000);
    });
  });

  describe('createAxiosConfig', () => {
    test('deve criar configuração axios válida', () => {
      const config = createAxiosConfig('https://api.dsm.darede.com.br');
      
      expect(config).toHaveProperty('baseURL');
      expect(config).toHaveProperty('withCredentials');
      expect(config).toHaveProperty('headers');
      expect(config).toHaveProperty('timeout');
      expect(config.baseURL).toBe('https://api.dsm.darede.com.br');
    });

    test('deve permitir override de opções', () => {
      const options = {
        withCredentials: false,
        timeout: 60000
      };
      
      const config = createAxiosConfig('https://api.dsm.darede.com.br', options);
      
      expect(config.withCredentials).toBe(false);
      expect(config.timeout).toBe(60000);
    });
  });

  describe('validateCorsConfig', () => {
    test('deve detectar withCredentials com URL externa', () => {
      const config = {
        withCredentials: true,
        url: 'https://cognito.amazonaws.com',
        headers: { 'Content-Type': 'application/json' }
      };
      
      const warnings = validateCorsConfig(config);
      
      expect(warnings).toHaveLength(1);
      expect(warnings[0]).toContain('withCredentials=true com URL externa');
    });

    test('deve detectar falta de Content-Type', () => {
      const config = {
        withCredentials: false,
        url: '/api/test',
        headers: {}
      };
      
      const warnings = validateCorsConfig(config);
      
      expect(warnings).toHaveLength(1);
      expect(warnings[0]).toContain('Header Content-Type não definido');
    });

    test('deve retornar array vazio para configuração válida', () => {
      const config = {
        withCredentials: true,
        url: '/api/test',
        headers: { 'Content-Type': 'application/json' }
      };
      
      const warnings = validateCorsConfig(config);
      
      expect(warnings).toHaveLength(0);
    });

    test('deve detectar múltiplos problemas', () => {
      const config = {
        withCredentials: true,
        url: 'https://cognito.amazonaws.com',
        headers: {}
      };
      
      const warnings = validateCorsConfig(config);
      
      expect(warnings.length).toBeGreaterThan(1);
    });
  });

  describe('CORS_CONFIG constantes', () => {
    test('deve ter headers padrão definidos', () => {
      expect(CORS_CONFIG.DEFAULT_HEADERS).toEqual({
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      });
    });

    test('deve ter timeout padrão definido', () => {
      expect(CORS_CONFIG.DEFAULT_TIMEOUT).toBe(30000);
    });

    test('deve ter listas de APIs internas e externas', () => {
      expect(Array.isArray(CORS_CONFIG.INTERNAL_APIS)).toBe(true);
      expect(Array.isArray(CORS_CONFIG.EXTERNAL_APIS)).toBe(true);
      expect(CORS_CONFIG.INTERNAL_APIS.length).toBeGreaterThan(0);
      expect(CORS_CONFIG.EXTERNAL_APIS.length).toBeGreaterThan(0);
    });
  });
});

describe('Cenários de Integração CORS', () => {
  test('deve configurar corretamente para requisição de autenticação', () => {
    const config = getCorsConfig('/auth/login', 'https://api.dsm.darede.com.br');
    
    expect(config.withCredentials).toBe(true);
    expect(config.headers['Content-Type']).toBe('application/json');
  });

  test('deve configurar corretamente para Cognito', () => {
    const config = getCorsConfig('/oauth/token', 'https://cognito.amazonaws.com');
    
    expect(config.withCredentials).toBe(false);
    expect(config.headers['Content-Type']).toBe('application/json');
  });

  test('deve configurar corretamente para proxy local', () => {
    const config = getCorsConfig('/test', '/api');
    
    expect(config.withCredentials).toBe(true);
  });
});
