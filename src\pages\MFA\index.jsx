import { Row, Col, Image, Space, Divider, <PERSON><PERSON>, Alert } from "antd";
import Logo from "../../assets/images/logo_full.png";
import { LoadingOutlined, BugOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import React, { useEffect } from "react";
import jwt_decode from "jwt-decode";
import "antd/dist/antd.css";
import axios from "axios";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { cognitoPutRole } from "../../service/apiCognito";
import { authService } from "../../services/authService";
import { testApiConnectivity, testMFAFlow, testBackendConnectivity } from "../../utils/apiDiagnostic";
import { getDsmApiUrl } from "../../config/apiConfig";

export const MFA = () => {
  const navigate = useNavigate();

  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');

  const [isAuthenticating, setIsAuthenticating] = React.useState(false);
  const [hasAuthenticated, setHasAuthenticated] = React.useState(false);
  const [showDiagnostic, setShowDiagnostic] = React.useState(false);
  const [diagnosticResults, setDiagnosticResults] = React.useState(null);

  // Cache para evitar reutilização do mesmo código
  const processedCodesRef = React.useRef(new Set());

  // Limpar cache de códigos antigos periodicamente
  React.useEffect(() => {
    const cleanup = setInterval(() => {
      processedCodesRef.current.clear();
    }, 5 * 60 * 1000); // Limpar a cada 5 minutos

    return () => clearInterval(cleanup);
  }, []);

  // Função para executar diagnóstico
  const runDiagnostic = async () => {
    console.log('🔧 Executando diagnóstico...');
    setShowDiagnostic(true);

    try {
      // console.log('🌐 Testando conectividade do backend...');
      const backendResults = await testBackendConnectivity();

      // console.log('🔧 Testando APIs completas...');
      const apiResults = await testApiConnectivity();

      // console.log('🔐 Testando fluxo MFA...');
      const mfaResults = await testMFAFlow(code);

      setDiagnosticResults({
        timestamp: new Date().toISOString(),
        backend: backendResults,
        apis: apiResults,
        mfa: mfaResults
      });
    } catch (error) {
      console.error('Erro no diagnóstico:', error);
      setDiagnosticResults({
        error: 'Falha ao executar diagnóstico',
        details: error.message
      });
    }
  };

  const authenticate = async () => {
    // Verificar se há código válido primeiro
    if (!code || code.length < 10) {
      console.error('Código de autenticação inválido ou não encontrado');
      navigate("/login");
      return;
    }

    // Verificar se o código já foi processado
    if (processedCodesRef.current.has(code)) {
      console.warn('Código já foi processado anteriormente');
      return;
    }

    if (isAuthenticating || hasAuthenticated) {
      console.warn('Autenticação já em andamento ou já realizada...');
      return;
    }

    // Marcar código como processado ANTES de iniciar
    processedCodesRef.current.add(code);
    setIsAuthenticating(true);
    setHasAuthenticated(true);

    try {
      // console.log('🔐 Iniciando processo de autenticação MFA...');
      // console.log('📋 Código recebido:', code);
      // console.log('🌐 Ambiente:', process.env.REACT_APP_STAGE);
      // console.log('🔗 API Cognito:', process.env.REACT_APP_COGNITO_PARSE);
      // console.log('🔗 API Permission:', process.env.REACT_APP_API_PERMISSION);

      // Usa o novo serviço de autenticação com cookies HttpOnly
      // console.log('🚀 Chamando authService.authenticate...');
      const { token: id_token, user } = await authService.authenticate(code);
      // console.log('✅ Autenticação bem-sucedida:', { user: user.email });
      const { email } = user;

      // console.log('🔍 Buscando dados do usuário no Cognito...');
      const {
        data: { data },
      } = await axios.get(`${getDsmApiUrl()}cognito/read`, {
        headers: { Authorization: `Bearer ${id_token}` },
      });

      // console.log('📊 Dados recebidos do Cognito:', data?.length, 'usuários encontrados');

      // Procurar usuário tanto com email limpo quanto com prefixo azuread_
      let cognitoUser = data?.find((user) =>
        user.email === email ||
        user.user === email ||
        user.user === `azuread_${email}` ||
        user.email === email.replace(/^azuread_/, '')
      );

      // console.log('👤 Email do usuário:', email);
      // console.log('🔍 Usuário encontrado no Cognito:', cognitoUser);
      // console.log('🔑 Verificando permissões para stage:', process.env.REACT_APP_STAGE);

      if (
        cognitoUser[
          `${
            process.env.REACT_APP_STAGE !== "prod"
              ? process.env.REACT_APP_STAGE + "_"
              : ""
          }permission`
        ] === undefined
      ) {
      // console.log("Sem permissão...");

      const users = await dynamoGet("azure-ad-users");

      const permissions = await dynamoGet(
        `${process.env.REACT_APP_STAGE}-permissions`
      );

      let role;

      // Procurar usuário no Azure AD com diferentes variações do email
      let u = users.find((user) =>
        user.userPrincipalName === email ||
        user.userPrincipalName === `azuread_${email}` ||
        user.userPrincipalName === email.replace(/^azuread_/, '') ||
        user.email === email ||
        user.email === email.replace(/^azuread_/, '')
      );

      if (!u) {
        console.log("Usuário não encontrado...");
        return navigate("/unauthorized");
      }

      console.log("Usuário encontrado: ", u);

      switch (u?.role) {
        case "full_admin":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Admin"
          ).id;
          break;

        case "basic_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Básico"
          ).id;
          break;

        case "dcq_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "DCQ"
          ).id;
          break;

        case "financial_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Financeiro"
          ).id;
          break;

        case "project_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Projetos"
          ).id;
          break;

        case "sales_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Vendas"
          ).id;
          break;

        case "security_admin":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Admin Segurança"
          ).id;
          break;

        default:
          return navigate("/unauthorized");
      }

        authService.setUserPermission(role);

        await cognitoPutRole({
          role,
          user: cognitoUser.user || cognitoUser.email,
          stage: process.env.REACT_APP_STAGE,
        });

        return navigate("/");
      } else {
        authService.setUserPermission(
          cognitoUser[
            `${
              process.env.REACT_APP_STAGE !== "prod"
                ? process.env.REACT_APP_STAGE + "_"
                : ""
            }permission`
          ]
        );

        navigate("/");
      }
    } catch (error) {


      // Remover código do cache se houve erro para permitir nova tentativa
      processedCodesRef.current.delete(code);

      // Se o erro for de código expirado/inválido, redirecionar para login
      if (error.message.includes('invalid_grant') ||
          error.message.includes('Código de autenticação expirado') ||
          error.message.includes('Falha ao configurar autenticação segura')) {
        console.warn('🔄 Redirecionando para login devido a erro de autenticação');

        // Limpar URL para evitar reutilização do código
        window.history.replaceState({}, document.title, '/login');
        navigate("/login");
      } else {
        console.warn('🚫 Redirecionando para página não autorizada');
        navigate("/unauthorized");
      }
    } finally {
      setIsAuthenticating(false);
    }
  };

  useEffect(() => {
    let isMounted = true; 

    async function getData() {
      if (!isMounted) return;

      if (!isAuthenticating && !hasAuthenticated && code && !processedCodesRef.current.has(code)) {
        try {
          await authenticate();
        } catch (error) {
          console.error('Erro na autenticação no useEffect:', error);
          if (isMounted) {
            setIsAuthenticating(false);
            setHasAuthenticated(false);
          }
        }
      } else if (!code) {
        console.error('Código de autenticação não encontrado');
        if (isMounted) {
          navigate("/login");
        }
      }
    }

    getData().catch(error => {
      console.error('Erro não capturado no getData:', error);
    });

    return () => {
      isMounted = false;
    };
  }, []); 

  return (
    <>
      <Row
        align="middle"
        justify="center"
        style={{ minHeight: "100vh", background: "#ebedef" }}
      >
        <Col
          xs={18}
          lg={12}
          xl={6}
          style={{
            display: "flex",
            justifyContent: "center",
            padding: "2em",
            borderRadius: "10px",
            border: "1px solid #c9c9c9",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <Space direction="vertical" align="center" size="middle">
            <Image preview={false} src={Logo}></Image>
            <Divider>
              Aguarde um momento...
              <br />
              Estamos te autenticando!
            </Divider>

            {/* Mostrar diagnóstico se houver erro */}
            {!isAuthenticating && !hasAuthenticated && (
              <Space direction="vertical" align="center" size="small">
                <Button
                  type="dashed"
                  icon={<BugOutlined />}
                  onClick={runDiagnostic}
                  size="small"
                >
                  Executar Diagnóstico
                </Button>

                {showDiagnostic && diagnosticResults && (
                  <Alert
                    message="Diagnóstico Completo de Conectividade"
                    description={
                      <div style={{ textAlign: 'left', fontSize: '11px', maxHeight: '300px', overflowY: 'auto' }}>
                        {diagnosticResults.error ? (
                          <p style={{ color: 'red' }}><strong>Erro:</strong> {diagnosticResults.error}</p>
                        ) : (
                          <>
                            <p><strong>Timestamp:</strong> {new Date(diagnosticResults.timestamp).toLocaleString()}</p>

                            {diagnosticResults.backend && (
                              <>
                                <p><strong>🌐 Conectividade Backend:</strong></p>
                                <ul style={{ marginLeft: '10px' }}>
                                  {diagnosticResults.backend.map((test, index) => (
                                    <li key={index}>
                                      {test.name}: {test.status === 'success' ? '✅' : '❌'}
                                      {test.status === 'error' && ` (${test.error})`}
                                    </li>
                                  ))}
                                </ul>
                              </>
                            )}

                            {diagnosticResults.apis && (
                              <>
                                <p><strong>🔧 Testes de API:</strong></p>
                                <ul style={{ marginLeft: '10px' }}>
                                  {diagnosticResults.apis.tests?.map((test, index) => (
                                    <li key={index}>
                                      {test.name}: {test.status === 'success' ? '✅' : '❌'}
                                      {test.status === 'error' && ` (${test.error})`}
                                    </li>
                                  ))}
                                </ul>
                              </>
                            )}

                            {diagnosticResults.mfa && (
                              <>
                                <p><strong>🔐 Teste MFA:</strong></p>
                                <p style={{ marginLeft: '10px' }}>
                                  Status: {diagnosticResults.mfa.success ? '✅ Sucesso' : '❌ Falha'}
                                  {diagnosticResults.mfa.error && ` (${diagnosticResults.mfa.error})`}
                                </p>
                              </>
                            )}
                          </>
                        )}
                      </div>
                    }
                    type={diagnosticResults.error ? 'error' : 'info'}
                    showIcon
                    style={{ maxWidth: '500px', fontSize: '11px' }}
                  />
                )}
              </Space>
            )}
          </Space>

          {isAuthenticating && <LoadingOutlined style={{ fontSize: "48px" }} />}
        </Col>
      </Row>
    </>
  );
};
