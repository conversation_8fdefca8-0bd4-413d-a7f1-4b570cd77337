/**
 * App Principal com Autenticação EXCLUSIVAMENTE HttpOnly
 * Implementação 100% segura sem Bearer tokens
 */

import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import ptBR from 'antd/locale/pt_BR';
import { PureHttpOnlyAuthProvider } from './contexts/PureHttpOnlyAuthProvider';
import { HttpOnlyAuthInitializer } from './components/Auth/HttpOnlyAuthInitializer';
import { PrivateRoute } from './components/Auth/PrivateRoute';
import { LoginForm } from './components/Auth/LoginForm';
import { Home } from './pages/Home';
import { MFA } from './pages/MFA';
import { Logoff } from './pages/Logoff';
import { Unauthorized } from './pages/Unauthorized';

// Exemplos (apenas desenvolvimento)
import { HttpOnlyAuthExamples } from './examples/HttpOnlyAuthExamples';

import 'antd/dist/reset.css';
import './App.css';

/**
 * Configuração de tema do Ant Design
 */
const antdTheme = {
  token: {
    colorPrimary: '#00B050',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    borderRadius: 6,
    wireframe: false,
  },
  components: {
    Button: {
      borderRadius: 6,
    },
    Card: {
      borderRadius: 8,
    },
    Modal: {
      borderRadius: 8,
    },
    Alert: {
      borderRadius: 6,
    },
  },
};

/**
 * Componente de rotas protegidas HttpOnly
 */
const HttpOnlyAppRoutes = () => (
  <Routes>
    {/* Rotas públicas */}
    <Route path="/login" element={<LoginForm />} />
    <Route path="/mfa" element={<MFA />} />
    <Route path="/logoff" element={<Logoff />} />
    <Route path="/unauthorized" element={<Unauthorized />} />
    
    {/* Rotas protegidas via HttpOnly */}
    <Route 
      path="/" 
      element={
        <PrivateRoute>
          <Home />
        </PrivateRoute>
      } 
    />
    
    <Route 
      path="/home" 
      element={
        <PrivateRoute>
          <Home />
        </PrivateRoute>
      } 
    />
    
    {/* Exemplos HttpOnly (apenas em desenvolvimento) */}
    {process.env.NODE_ENV === 'development' && (
      <Route 
        path="/examples/httponly-auth" 
        element={
          <PrivateRoute>
            <HttpOnlyAuthExamples />
          </PrivateRoute>
        } 
      />
    )}
    
    {/* Rotas com permissões específicas */}
    <Route 
      path="/admin" 
      element={
        <PrivateRoute requiredPermission="admin">
          <div style={{ padding: '24px' }}>
            <h1>🔒 Área Administrativa (HttpOnly)</h1>
            <p>Esta área é protegida exclusivamente por cookies HttpOnly.</p>
          </div>
        </PrivateRoute>
      } 
    />
    
    <Route 
      path="/manager" 
      element={
        <PrivateRoute requiredPermissions={['admin', 'manager']}>
          <div style={{ padding: '24px' }}>
            <h1>👥 Área Gerencial (HttpOnly)</h1>
            <p>Acesso para administradores e gerentes via cookies seguros.</p>
          </div>
        </PrivateRoute>
      } 
    />
    
    {/* Fallback para rotas não encontradas */}
    <Route 
      path="*" 
      element={
        <PrivateRoute fallbackPath="/login">
          <div style={{ padding: '24px', textAlign: 'center' }}>
            <h1>404 - Página não encontrada</h1>
            <p>A página solicitada não existe.</p>
          </div>
        </PrivateRoute>
      } 
    />
  </Routes>
);

/**
 * Componente principal da aplicação HttpOnly
 */
export const AppPureHttpOnly = () => {
  return (
    <ConfigProvider 
      locale={ptBR}
      theme={antdTheme}
    >
      <PureHttpOnlyAuthProvider>
        <HttpOnlyAuthInitializer>
          <BrowserRouter>
            <div className="app">
              <HttpOnlyAppRoutes />
            </div>
          </BrowserRouter>
        </HttpOnlyAuthInitializer>
      </PureHttpOnlyAuthProvider>
    </ConfigProvider>
  );
};

/**
 * Exemplo de uso em componentes HttpOnly
 */
export const HttpOnlyUsageExamples = () => {
  const exampleCode = `
// 1. Hook HttpOnly básico
import { useHttpOnlyAuth } from './hooks/usePureHttpOnlyAuth';

const MyComponent = () => {
  const { 
    isAuthenticated, 
    user, 
    login, 
    logout, 
    hasPermission 
  } = useHttpOnlyAuth();

  if (!isAuthenticated) {
    return <div>Faça login para continuar</div>;
  }

  return (
    <div>
      <h1>Bem-vindo, {user.name}!</h1>
      <p>🔒 Autenticado via cookies HttpOnly</p>
      {hasPermission('admin') && (
        <button>Área Admin</button>
      )}
      <button onClick={logout}>Sair</button>
    </div>
  );
};

// 2. Hook de status HttpOnly
import { useHttpOnlyAuthStatus } from './hooks/usePureHttpOnlyAuth';

const StatusComponent = () => {
  const { isAuthenticated, isLoading, status, isReady } = useHttpOnlyAuthStatus();
  
  if (isLoading) return <div>Carregando...</div>;
  
  return (
    <div>
      <p>Status: {status}</p>
      <p>Método: HttpOnly exclusivo</p>
      <p>Bearer tokens: Desabilitados</p>
    </div>
  );
};

// 3. Hook de permissões HttpOnly
import { useHttpOnlyPermissions } from './hooks/usePureHttpOnlyAuth';

const PermissionComponent = () => {
  const { hasAny, hasAll, missing } = useHttpOnlyPermissions(['read', 'write']);
  
  return (
    <div>
      <p>Tem alguma permissão: {hasAny ? 'Sim' : 'Não'}</p>
      <p>Tem todas: {hasAll ? 'Sim' : 'Não'}</p>
      <p>Faltando: {missing.join(', ')}</p>
      <p>🍪 Verificado via cookies HttpOnly</p>
    </div>
  );
};

// 4. Hook de usuário atual HttpOnly
import { useHttpOnlyCurrentUser } from './hooks/usePureHttpOnlyAuth';

const UserComponent = () => {
  const { user, email, displayName, initials } = useHttpOnlyCurrentUser();
  
  return (
    <div>
      <h2>{displayName}</h2>
      <p>{email}</p>
      <div className="avatar">{initials}</div>
      <small>🔒 Dados obtidos via cookies seguros</small>
    </div>
  );
};

// 5. Proteção de rotas HttpOnly
import { PrivateRoute } from './components/Auth/PrivateRoute';

const ProtectedPage = () => (
  <PrivateRoute requiredPermission="admin">
    <AdminPanel />
  </PrivateRoute>
);

// 6. Login com código OAuth HttpOnly
const LoginPage = () => {
  const { login } = useHttpOnlyAuth();
  
  const handleLogin = async (code) => {
    try {
      await login(code);
      // Token será armazenado em cookie HttpOnly no backend
      // Nenhum token ficará no frontend
    } catch (error) {
      console.error('Erro no login:', error);
    }
  };
  
  return <LoginForm onLogin={handleLogin} />;
};

// 7. Configuração no App principal
import { PureHttpOnlyAuthProvider } from './contexts/PureHttpOnlyAuthProvider';
import { HttpOnlyAuthInitializer } from './components/Auth/HttpOnlyAuthInitializer';

function App() {
  return (
    <PureHttpOnlyAuthProvider>
      <HttpOnlyAuthInitializer>
        <YourAppContent />
      </HttpOnlyAuthInitializer>
    </PureHttpOnlyAuthProvider>
  );
}
  `;

  return (
    <div style={{ padding: '24px' }}>
      <h2>🔒 Exemplos de Uso HttpOnly Exclusivo</h2>
      <div style={{ 
        background: '#f5f5f5', 
        padding: '16px', 
        borderRadius: '4px',
        border: '2px solid #00B050'
      }}>
        <h3 style={{ color: '#00B050' }}>✅ Características de Segurança:</h3>
        <ul>
          <li>🍪 <strong>Cookies HttpOnly exclusivos</strong> - Tokens nunca expostos ao JavaScript</li>
          <li>🚫 <strong>Bearer tokens desabilitados</strong> - Nenhum token no localStorage/headers</li>
          <li>🧹 <strong>localStorage limpo</strong> - Remoção automática de tokens legados</li>
          <li>🔧 <strong>Interceptors seguros</strong> - withCredentials=true automático</li>
          <li>🔄 <strong>Refresh automático</strong> - Via cookies HttpOnly</li>
          <li>🛡️ <strong>Proteção CSRF</strong> - SameSite cookies</li>
        </ul>
      </div>
      <pre style={{ 
        background: '#f5f5f5', 
        padding: '16px', 
        borderRadius: '4px',
        overflow: 'auto',
        fontSize: '12px',
        marginTop: '16px'
      }}>
        {exampleCode}
      </pre>
    </div>
  );
};

export default AppPureHttpOnly;
