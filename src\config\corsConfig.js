/**
 * Configuração CORS centralizada e padronizada
 * Resolve conflitos entre diferentes serviços de autenticação
 */

import { config } from '../utils/validateEnvironment';
import { logger } from '../utils/logger';

/**
 * Configurações CORS padronizadas
 */
export const CORS_CONFIG = {
  // Headers padrão para todas as requisições
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },

  // Timeout padrão
  DEFAULT_TIMEOUT: 30000,

  // URLs internas que suportam cookies HttpOnly
  INTERNAL_APIS: [
    'api.dsm.darede.com.br',
    '/api', // Proxy local
    config.apiUrl
  ],

  // URLs externas que NÃO devem usar withCredentials
  EXTERNAL_APIS: [
    'cognito',
    'amazonaws.com',
    'auth0.com'
  ]
};

/**
 * Determina se uma URL é de API interna
 */
export const isInternalAPI = (url, baseURL) => {
  if (!url) return false;
  
  const fullUrl = baseURL ? `${baseURL}${url}` : url;
  
  return CORS_CONFIG.INTERNAL_APIS.some(internalUrl => 
    fullUrl.includes(internalUrl)
  );
};

/**
 * Determina se uma URL é de API externa
 */
export const isExternalAPI = (url, baseURL) => {
  if (!url) return false;
  
  const fullUrl = baseURL ? `${baseURL}${url}` : url;
  
  return CORS_CONFIG.EXTERNAL_APIS.some(externalUrl => 
    fullUrl.includes(externalUrl)
  );
};

/**
 * Configuração CORS inteligente baseada na URL
 */
export const getCorsConfig = (url, baseURL, options = {}) => {
  const isInternal = isInternalAPI(url, baseURL);
  const isExternal = isExternalAPI(url, baseURL);
  
  const corsConfig = {
    // withCredentials baseado no tipo de API
    withCredentials: isInternal && !isExternal,
    
    // Headers padrão
    headers: {
      ...CORS_CONFIG.DEFAULT_HEADERS,
      ...options.headers
    },
    
    // Timeout
    timeout: options.timeout || CORS_CONFIG.DEFAULT_TIMEOUT
  };

  logger.debug('Configuração CORS determinada', {
    url,
    baseURL,
    isInternal,
    isExternal,
    withCredentials: corsConfig.withCredentials
  });

  return corsConfig;
};

/**
 * Cria configuração axios padronizada
 */
export const createAxiosConfig = (baseURL, options = {}) => {
  const corsConfig = getCorsConfig('', baseURL, options);
  
  return {
    baseURL,
    ...corsConfig,
    ...options // Permite override se necessário
  };
};

/**
 * Valida configuração CORS
 */
export const validateCorsConfig = (config) => {
  const warnings = [];
  
  // Verificar se withCredentials está sendo usado com URLs externas
  if (config.withCredentials && config.url) {
    const isExternal = isExternalAPI(config.url, config.baseURL);
    if (isExternal) {
      warnings.push(`withCredentials=true com URL externa: ${config.url}`);
    }
  }
  
  // Verificar headers obrigatórios
  if (!config.headers || !config.headers['Content-Type']) {
    warnings.push('Header Content-Type não definido');
  }
  
  if (warnings.length > 0) {
    logger.warn('Problemas de configuração CORS detectados', { warnings });
  }
  
  return warnings;
};

/**
 * Middleware para validação automática de CORS
 */
export const corsValidationMiddleware = (config) => {
  validateCorsConfig(config);
  return config;
};

export default {
  CORS_CONFIG,
  isInternalAPI,
  isExternalAPI,
  getCorsConfig,
  createAxiosConfig,
  validateCorsConfig,
  corsValidationMiddleware
};
