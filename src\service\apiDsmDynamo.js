import Axios from "axios";
import useS<PERSON> from "swr";
import * as proposalController from "../controllers/Proposals/proposal-controller";
import * as customerController from "../controllers/clients/clientsController";
import { uniqByKeepLast } from "../utils/uniqueByKeepLast";
import { formatTableData } from "../controllers/switchRoles/switch-role-controller";
import { dsmApiProvider } from "../provider/dsm-api-provider";
import { switchRoleProvider } from "../provider/switch-role-api-provider";
import { format } from "date-fns";
import { httpOnlyAuthService } from "../services/httpOnlyAuthService";
import { getDsmApiUrl } from "../config/apiConfig";

const dsmProvider = dsmApiProvider();
const switchRoleApiProvider = switchRoleProvider();

export const dynamoSwitchRoleAccessesPerUser = async (body) => {
  const currentUser = httpOnlyAuthService.getUserData().name;
  const { data } = await Axios.get(
    getDsmApiUrl() + `read/switch-role-exists/${currentUser}/all`,
    {
      headers: httpOnlyAuthService.getLegacyAuthHeaders(),
    }
  );

  return data.data.Items;
};

export const dynamoGetById = async (tableName, id) => {
  console.log("dynamoGetById - Fazendo requisição para:", getDsmApiUrl() + "read/id/" + id);
  console.log("dynamoGetById - Headers:", { dynamodb: tableName });

  try {
    const { data } = await Axios.get(
      getDsmApiUrl() + "read/id/" + id,
      {
        headers: {
          dynamodb: tableName,
          ...httpOnlyAuthService.getLegacyAuthHeaders(),
        },
      }
    );

    console.log("dynamoGetById - Resposta completa:", data);
    console.log("dynamoGetById - data.data:", data.data);
    console.log("dynamoGetById - Item retornado:", data.data?.Item);

    return data.data.Item;
  } catch (error) {
    console.error("Error in dynamoGetById:", error);
    console.error("Error details:", {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url
    });
    return null;
  }
};

export function useDynamoGet(tableName) {
  const { data, error, mutate } = useSWR(tableName, async () => {
    if (tableName.endsWith("-customers")) {
      const list = await customerController.getAllCustomers();
      return list.Items;
    }

    const { data } = await Axios.get(
      getDsmApiUrl() + "read/all/0",
      {
        headers: {
          dynamodb: tableName,
          ...httpOnlyAuthService.getLegacyAuthHeaders(),
        },
      }
    );

    return data.data.Items;
  });

  return { data, error, mutate };
}

export function useDynamoGetCustomers(tableName, route, queryParams) {
  const { data, error, mutate } = useSWR(tableName, async () => {
    const { data } = await Axios.get(
      getDsmApiUrl() + `${route}?status=1`,
      {
        headers: {
          dynamodb: tableName,
          ...httpOnlyAuthService.getLegacyAuthHeaders(),
        },
      }
    );

    return data.data.Items;
  });

  return { data, error, mutate };
}

export function useDynamoGetQuery(tableName) {
  const { data, error, mutate } = useSWR(tableName, async () => {
    const { data } = await Axios.get(
      "https://wqm50qqfo4.execute-api.us-east-1.amazonaws.com/prod/read/all/0",
      {
        headers: {
          dynamodb: tableName,
          ...httpOnlyAuthService.getLegacyAuthHeaders(),
        },
      }
    );

    return data.data.Items;
  });

  return { data, error, mutate };
}

export const dolarGet = async (month, year) => {
  const { data } = await Axios.get(
    getDsmApiUrl() + `read/dolar/${month}/${year}`,
    {
      headers: httpOnlyAuthService.getLegacyAuthHeaders(),
    }
  );

  return data.data;
};

export const genericPost = async (route, body) => {
  const { data } = await Axios.post(
    getDsmApiUrl() + route,
    { ...body },
    {
      headers: httpOnlyAuthService.getLegacyAuthHeaders(),
    }
  );

  return data.data.Items;
};

export const dynamoGet = async (tableName) => {
  try {
    if (tableName.endsWith("-customers")) {
      const list = await customerController.getAllCustomers();
      return list.Items;
    }

    if (tableName.endsWith("-proposals")) {
      const list = await proposalController.getAllProposals();
      return list.Items;
    }

    const { data } = await Axios.get(
      getDsmApiUrl() + "read/all/0",
      {
        headers: {
          dynamodb: tableName,
          ...httpOnlyAuthService.getLegacyAuthHeaders(),
        },
      }
    );
    return data.data.Items;
  } catch (e) {
    console.log(e);
    return [];
  }
};

export const dynamoPost = async (tableName, body) => {
  if (!tableName === `${process.env.REACT_APP_STAGE}-tickets`) {
    delete body.id;
  }

  const { data } = await Axios.post(
    getDsmApiUrl() + "create",
    { ...body },
    {
      headers: {
        dynamodb: tableName,
        ...httpOnlyAuthService.getLegacyAuthHeaders(),
      },
    }
  );

  return data;
};

export const dynamoPut = async (tableName, id, body) => {
  let obj = {};

  Object.entries(body).forEach((o) => {
    if (o[0] !== "id" && o[1] !== undefined && o[1] !== null) {
      obj[o[0]] = { Action: "PUT", Value: o[1] };
    }
  });

  const { data } = await Axios.put(
    getDsmApiUrl() + "update/" + id,
    { ...obj },
    {
      headers: {
        dynamodb: tableName,
        ...httpOnlyAuthService.getLegacyAuthHeaders(),
      },
    }
  );

  return data;
};

export const dynamoDelete = async (tableName, id) => {
  const { data } = await Axios.delete(
    getDsmApiUrl() + "delete/" + id,
    {
      headers: {
        dynamodb: tableName,
        ...httpOnlyAuthService.getLegacyAuthHeaders(),
      },
    }
  );

  return data;
};

export const dynamoGetSwitchRoleSolicitations = async () => {
  let solicitations = [];
  try {
    let { data } = await dsmProvider.get("read/switch-role");

    let { Items } = data.data;
    let ssoReadData = await switchRoleApiProvider.post("sso-read", { id: 0 });

    ssoReadData = ssoReadData.data.body.Items;

    solicitations = formatTableData(Items);

    solicitations.forEach((e) => {
      for (let account of ssoReadData) {
        if (e.username === account.user) {
          e.arn = account.arn;
        }
      }
    });

    let tkt = [];
    let objectParatemers = [
      "username",
      "solicitations",
      "client_ticket",
      "client_name",
      "allowed",
    ];
    const newArr = uniqByKeepLast(solicitations, (i) => i.client_user);

    newArr.forEach((e) => {
      let obj = {};
      objectParatemers.forEach((parameter) => {
        if (parameter === "allowed")
          return (obj[parameter] = solicitations
            .filter((i) => i.client_user === e[0])
            .every((v) => v.allowed));
        if (parameter === "solicitations")
          return (obj[parameter] = solicitations.filter(
            (i) => i.client_user === e[0]
          ));
        return (obj[parameter] = solicitations.filter(
          (i) => i.client_user === e[0]
        )[0]?.[parameter]);
      });

      return tkt.push(obj);
    });
    return tkt;
  } catch (err) {
    console.log(err);
  }
};

export const dynamoGetPermissionSets = async () => {
  let arr = [];

  let { data } = await switchRoleApiProvider.post("sso-read", { id: 0 });

  data.body.Items.forEach((user) => {
    arr.push({
      linked: user.linked,
      user: user.user,
      arn: user.arn,
      id: user.id,
    });
  });
  return arr;
};

export const invokeStateMachine = async (body) => {
  const dsmProviderIntance = dsmApiProvider();
  try {
    await dsmProviderIntance.post("state-machine", body);
  } catch (e) {
    console.log(e);
  }
};

export const checkDaredeFullRole = async (account_id) => {
  try {
    const response = await dsmProvider.post("check-darede-full-role", {
      account: account_id,
    });

    if (response.status === 200 && response.data.success) {
      return response.data;
    } else {
      throw new Error(
        "A role darede-full NÃO existe ou não pode ser assumida."
      );
    }
  } catch (e) {
    console.error("Erro capturado:", e);
    throw e;
  }
};

export const sendWebhookNotification = (accountID, client, data) => {
  const body = {
    body: {
      "@type": "MessageCard",
      "@context": "https://schema.org/extensions",
      summary: "Issue *********",
      themeColor: "0d9347",
      title: "Solicitação de Switch Role",
      sections: [
        {
          activityTitle: httpOnlyAuthService.getUserData().name,
          activitySubtitle: format(new Date(), "dd/MM/yyyy, HH:mm"),
          activityImage:
            "https://cdn.iconscout.com/icon/free/png-256/switch-1470433-1244947.png",
          text: `Estou solicitando acesso para a(s) conta(s) '${accountID.join(
            ", "
          )}' do cliente ${
            client?.names?.fantasy_name
          }, tratando-se do ticket ${data.ticket_id}.`,
        },
      ],
    },
  };
  try {
    dsmProvider.post("send/notification", body);
  } catch (e) {
    console.log(e);
  }
};

export const createContract = async (body) => {
  const provider = dsmApiProvider();
  try {
    await provider.post("contracts/create", body);
  } catch (e) {
    console.log(e);
    throw e;
  }
};

export const getItemsByDynamicIndex = async (tableName, index, indexValue) => {
  const provider = dsmApiProvider();
  const headers = {
    dynamodb: tableName,
  };

  const queryParams = {
    indexName: index,
  };

  try {
    const { data } = await provider.get(
      `read/indexKey/${indexValue}?indexName=${index}`,
      {
        headers: headers,
      }
    );

    return data.data.Items;
  } catch (e) {
    console.log(e);
    return [];
  }
};
