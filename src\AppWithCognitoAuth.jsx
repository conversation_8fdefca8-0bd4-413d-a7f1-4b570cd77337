/**
 * App principal com AuthProvider Cognito integrado
 * Exemplo de configuração completa
 */

import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import ptBR from 'antd/locale/pt_BR';

import { CognitoAuthProvider } from './contexts/CognitoAuthProvider';
import { AuthInitializer } from './components/Auth/AuthInitializer';
import { PrivateRoute } from './components/Auth/PrivateRoute';
import { LoginForm } from './components/Auth/LoginForm';
import { Home } from './pages/Home';
import { MFA } from './pages/MFA';
import { Logoff } from './pages/Logoff';
import { Unauthorized } from './pages/Unauthorized';

// Exemplos
import { CognitoAuthExamples } from './examples/CognitoAuthExamples';

// Estilos
import 'antd/dist/reset.css';
import './App.css';

/**
 * Configuração de tema do Ant Design
 */
const antdTheme = {
  token: {
    colorPrimary: '#00B050',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    borderRadius: 6,
    wireframe: false,
  },
  components: {
    Button: {
      borderRadius: 6,
    },
    Card: {
      borderRadius: 8,
    },
    Modal: {
      borderRadius: 8,
    },
    Alert: {
      borderRadius: 6,
    },
  },
};

/**
 * Componente de rotas protegidas
 */
const AppRoutes = () => (
  <Routes>
    {/* Rotas públicas */}
    <Route path="/login" element={<LoginForm />} />
    <Route path="/mfa" element={<MFA />} />
    <Route path="/logoff" element={<Logoff />} />
    <Route path="/unauthorized" element={<Unauthorized />} />
    
    {/* Rotas protegidas */}
    <Route 
      path="/" 
      element={
        <PrivateRoute>
          <Home />
        </PrivateRoute>
      } 
    />
    
    <Route 
      path="/home" 
      element={
        <PrivateRoute>
          <Home />
        </PrivateRoute>
      } 
    />
    
    {/* Exemplos (apenas em desenvolvimento) */}
    {process.env.NODE_ENV === 'development' && (
      <Route 
        path="/examples/auth" 
        element={
          <PrivateRoute>
            <CognitoAuthExamples />
          </PrivateRoute>
        } 
      />
    )}
    
    {/* Rotas com permissões específicas */}
    <Route 
      path="/admin" 
      element={
        <PrivateRoute requiredPermission="admin">
          <div>Área Administrativa</div>
        </PrivateRoute>
      } 
    />
    
    <Route 
      path="/manager" 
      element={
        <PrivateRoute requiredPermissions={['admin', 'manager']}>
          <div>Área Gerencial</div>
        </PrivateRoute>
      } 
    />
    
    {/* Fallback para rotas não encontradas */}
    <Route 
      path="*" 
      element={
        <PrivateRoute fallbackPath="/login">
          <div>Página não encontrada</div>
        </PrivateRoute>
      } 
    />
  </Routes>
);

/**
 * Componente principal da aplicação
 */
export const AppWithCognitoAuth = () => {
  return (
    <ConfigProvider 
      locale={ptBR}
      theme={antdTheme}
    >
      <CognitoAuthProvider>
        <AuthInitializer>
          <BrowserRouter>
            <div className="app">
              <AppRoutes />
            </div>
          </BrowserRouter>
        </AuthInitializer>
      </CognitoAuthProvider>
    </ConfigProvider>
  );
};

/**
 * Exemplo de uso em componentes
 */
export const ExampleUsageInComponents = () => {
  // Exemplo de uso dos hooks
  const exampleCode = `
// 1. Hook básico de autenticação
import { useAuth } from './hooks/useCognitoAuth';

const MyComponent = () => {
  const { 
    isAuthenticated, 
    user, 
    login, 
    logout, 
    hasPermission 
  } = useAuth();

  if (!isAuthenticated) {
    return <div>Faça login para continuar</div>;
  }

  return (
    <div>
      <h1>Bem-vindo, {user.name}!</h1>
      {hasPermission('admin') && (
        <button>Área Admin</button>
      )}
      <button onClick={logout}>Sair</button>
    </div>
  );
};

// 2. Hook de status de autenticação
import { useAuthStatus } from './hooks/useCognitoAuth';

const StatusComponent = () => {
  const { isAuthenticated, isLoading, status } = useAuthStatus();
  
  if (isLoading) return <div>Carregando...</div>;
  
  return <div>Status: {status}</div>;
};

// 3. Hook de permissões
import { usePermissions } from './hooks/useCognitoAuth';

const PermissionComponent = () => {
  const { hasAny, hasAll, missing } = usePermissions(['read', 'write']);
  
  return (
    <div>
      <p>Tem alguma permissão: {hasAny ? 'Sim' : 'Não'}</p>
      <p>Tem todas: {hasAll ? 'Sim' : 'Não'}</p>
      <p>Faltando: {missing.join(', ')}</p>
    </div>
  );
};

// 4. Hook de usuário atual
import { useCurrentUser } from './hooks/useCognitoAuth';

const UserComponent = () => {
  const { user, email, displayName, initials } = useCurrentUser();
  
  return (
    <div>
      <h2>{displayName}</h2>
      <p>{email}</p>
      <div className="avatar">{initials}</div>
    </div>
  );
};

// 5. Proteção de rotas
import { PrivateRoute } from './components/Auth/PrivateRoute';

const ProtectedPage = () => (
  <PrivateRoute requiredPermission="admin">
    <AdminPanel />
  </PrivateRoute>
);

// 6. Proteção de componentes
import { AuthGuard } from './components/Auth/AuthGuard';

const ConditionalComponent = () => (
  <div>
    <h1>Página Pública</h1>
    <AuthGuard requiredPermission="edit">
      <button>Editar</button>
    </AuthGuard>
    <AuthGuard requiredPermissions={['admin', 'manager']}>
      <button>Gerenciar</button>
    </AuthGuard>
  </div>
);

// 7. Login com código OAuth
const LoginPage = () => {
  const { login } = useAuth();
  
  const handleLogin = async (code) => {
    try {
      await login(code);
      // Usuário será redirecionado automaticamente
    } catch (error) {
      console.error('Erro no login:', error);
    }
  };
  
  return <LoginForm onLogin={handleLogin} />;
};
  `;

  return (
    <div style={{ padding: '24px' }}>
      <h2>Exemplos de Uso nos Componentes</h2>
      <pre style={{ 
        background: '#f5f5f5', 
        padding: '16px', 
        borderRadius: '4px',
        overflow: 'auto',
        fontSize: '12px'
      }}>
        {exampleCode}
      </pre>
    </div>
  );
};

export default AppWithCognitoAuth;
